import encoding_init  # 必须在最开始导入，自动配置编码
import os
import asyncio
import threading
import logging
import json
from datetime import datetime, date

from flask import Flask, request, jsonify
from pathlib import Path

from browser_runner_constants import BrowserRunnerConstants
from conf import BASE_DIR, rabbitmq_setting, mysql_setting
from pika_client import PikaClient
from race_data_runner import RaceDataRunner
from result_data_runner import ResultDataRunner
from team_analysis_runner import TeamAnalysisRunner
from mysql_client import MySQLClient
from logging_config import setup_app_logging
from simple_scheduler import SimpleScheduler

app = Flask(__name__)

# 设置Flask应用的默认编码为UTF-8
app.config['JSON_AS_ASCII'] = False

# 设置Flask的werkzeug日志级别
logging.getLogger('werkzeug').setLevel(logging.ERROR)

# 设置应用日志
logger = setup_app_logging()

loop = None
mcp_mysql_client = None
scheduler = None
scheduler_task = None

@app.route('/get_races', methods=['POST'])
def get_races():
    race_data_runner = RaceDataRunner()
    loop.run_until_complete(race_data_runner.start())
    return getResponse(True, None)

@app.route('/update_scores', methods=['POST'])
def update_scores():
    """更新比赛比分"""
    result_data_runner = ResultDataRunner()
    loop.run_until_complete(result_data_runner.start())
    return getResponse(True, "比分更新完成")

@app.route('/get_team_analysis', methods=['POST'])
def get_team_analysis():
    """获取队伍分析数据"""
    team_analysis_runner = TeamAnalysisRunner()
    loop.run_until_complete(team_analysis_runner.start())
    return getResponse(True, "队伍分析数据获取完成")

@app.route('/scheduler/status', methods=['GET'])
def get_scheduler_status():
    """获取调度器状态"""
    global scheduler
    if scheduler is None:
        return getResponse(False, "调度器未启动")
    
    try:
        status = scheduler.get_status()
        return getResponse(True, status)
    except Exception as e:
        logger.error(f"获取调度器状态失败: {e}")
        return getResponse(False, f"获取状态失败: {str(e)}")

@app.route('/scheduler/stop', methods=['POST'])
def stop_scheduler():
    """停止调度器"""
    global scheduler
    if scheduler is None:
        return getResponse(False, "调度器未运行")
    
    try:
        scheduler.stop()
        return getResponse(True, "调度器已停止")
    except Exception as e:
        logger.error(f"停止调度器失败: {e}")
        return getResponse(False, f"停止失败: {str(e)}")

@app.route('/mcp/tools', methods=['GET'])
def list_mcp_tools():
    """列出MCP工具"""
    tools = [
        {
            "name": "get_today_upcoming_matches",
            "description": "获取今天还未开赛的比赛",
            "inputSchema": {
                "type": "object",
                "properties": {
                    "league_filter": {"type": "string", "description": "可选的联赛名称过滤器"},
                    "limit": {"type": "integer", "description": "返回结果的最大数量，默认50", "default": 50}
                }
            }
        },
        {
            "name": "get_matches_by_date",
            "description": "根据指定日期获取比赛",
            "inputSchema": {
                "type": "object",
                "properties": {
                    "match_date": {"type": "string", "description": "比赛日期，格式：YYYY-MM-DD"},
                    "status_filter": {"type": "string", "description": "可选的比赛状态过滤器"},
                    "limit": {"type": "integer", "description": "返回结果的最大数量，默认100", "default": 100}
                },
                "required": ["match_date"]
            }
        },
        {
            "name": "get_database_statistics",
            "description": "获取数据库统计信息",
            "inputSchema": {"type": "object", "properties": {}}
        }
    ]
    return jsonify({"tools": tools})

@app.route('/mcp/call', methods=['GET'])
def call_mcp_tool():
    """调用MCP工具"""
    tool_name = request.args.get('name')
    
    # 将查询参数转换为字典，并处理数字类型
    arguments = {}
    for key, value in request.args.items():
        if key == 'limit' and value.isdigit():
            arguments[key] = int(value)
        else:
            arguments[key] = value
    
    try:
        if tool_name == "get_today_upcoming_matches":
            result = loop.run_until_complete(get_today_upcoming_matches_sync(arguments))
        elif tool_name == "get_matches_by_date":
            result = loop.run_until_complete(get_matches_by_date_sync(arguments))
        elif tool_name == "get_database_statistics":
            result = loop.run_until_complete(get_database_statistics_sync(arguments))
        else:
            return jsonify({"error": f"未知的工具: {tool_name}"}), 400
        
        return jsonify({"result": result})
    except Exception as e:
        logger.error(f"MCP工具调用失败 {tool_name}: {e}")
        return jsonify({"error": str(e)}), 500

async def get_today_upcoming_matches_sync(arguments: dict) -> str:
    """获取今天还未开赛的比赛"""
    global mcp_mysql_client
    
    if mcp_mysql_client is None:
        mcp_mysql_client = MySQLClient(mysql_setting)
        await mcp_mysql_client.connect()
    
    league_filter = arguments.get("league_filter")
    limit = arguments.get("limit", 50)
    
    today = date.today().strftime('%Y-%m-%d')
    current_time = datetime.now()
    
    try:
        async with mcp_mysql_client.pool.acquire() as conn:
            async with conn.cursor() as cursor:
                base_sql = """
                SELECT id, league_name, home_team, away_team, match_time, 
                       home_score, away_score, match_status, created_at
                FROM race_matches
                WHERE DATE(match_time) = %s 
                AND match_time > %s
                AND match_status IN ('未开始', '延期')
                """
                
                params = [today, current_time]
                
                if league_filter:
                    base_sql += " AND league_name LIKE %s"
                    params.append(f"%{league_filter}%")
                
                base_sql += " ORDER BY match_time ASC LIMIT %s"
                params.append(limit)
                
                await cursor.execute(base_sql, params)
                results = await cursor.fetchall()
                
                if not results:
                    return "今天没有找到未开赛的比赛"
                
                matches_info = []
                matches_info.append(f"今天还未开赛的比赛 (共{len(results)}场):")
                matches_info.append("=" * 50)
                
                for match in results:
                    match_id, league, home, away, match_time, home_score, away_score, status, created = match
                    time_str = match_time.strftime('%H:%M')
                    score_str = f"{home_score}-{away_score}" if home_score is not None else "vs"
                    
                    matches_info.append(f"🏆 {league}")
                    matches_info.append(f"⏰ {time_str} | {home} {score_str} {away} ({status})")
                    matches_info.append("")
                
                return "\n".join(matches_info)
                
    except Exception as e:
        logger.error(f"查询今天未开赛比赛失败: {e}")
        raise

async def get_matches_by_date_sync(arguments: dict) -> str:
    """根据指定日期获取比赛"""
    global mcp_mysql_client
    
    if mcp_mysql_client is None:
        mcp_mysql_client = MySQLClient(mysql_setting)
        await mcp_mysql_client.connect()
    
    match_date = arguments["match_date"]
    status_filter = arguments.get("status_filter")
    limit = arguments.get("limit", 100)
    
    try:
        async with mcp_mysql_client.pool.acquire() as conn:
            async with conn.cursor() as cursor:
                base_sql = """
                SELECT id, league_name, home_team, away_team, match_time, 
                       home_score, away_score, match_status, created_at
                FROM race_matches
                WHERE DATE(match_time) = %s
                """
                
                params = [match_date]
                
                if status_filter:
                    base_sql += " AND match_status = %s"
                    params.append(status_filter)
                
                base_sql += " ORDER BY match_time ASC LIMIT %s"
                params.append(limit)
                
                await cursor.execute(base_sql, params)
                results = await cursor.fetchall()
                
                if not results:
                    return f"{match_date} 没有找到比赛"
                
                matches_info = []
                status_text = f"({status_filter})" if status_filter else ""
                matches_info.append(f"{match_date} 的比赛{status_text} (共{len(results)}场):")
                matches_info.append("=" * 50)
                
                for match in results:
                    match_id, league, home, away, match_time, home_score, away_score, status, created = match
                    time_str = match_time.strftime('%H:%M')
                    score_str = f"{home_score}-{away_score}" if home_score is not None else "vs"
                    
                    matches_info.append(f"🏆 {league} | ⏰ {time_str}")
                    matches_info.append(f"   {home} {score_str} {away} ({status})")
                    matches_info.append("")
                
                return "\n".join(matches_info)
                
    except Exception as e:
        logger.error(f"查询指定日期比赛失败: {e}")
        raise

async def get_database_statistics_sync(arguments: dict) -> str:
    """获取数据库统计信息"""
    global mcp_mysql_client
    
    if mcp_mysql_client is None:
        mcp_mysql_client = MySQLClient(mysql_setting)
        await mcp_mysql_client.connect()
    
    try:
        async with mcp_mysql_client.pool.acquire() as conn:
            async with conn.cursor() as cursor:
                # 总比赛数
                await cursor.execute("SELECT COUNT(*) FROM race_matches")
                total_matches = (await cursor.fetchone())[0]
                
                # 按状态统计
                await cursor.execute("""
                    SELECT match_status, COUNT(*) 
                    FROM race_matches 
                    GROUP BY match_status
                """)
                status_stats = await cursor.fetchall()
                
                # 今天的比赛统计
                await cursor.execute("""
                    SELECT COUNT(*) FROM race_matches 
                    WHERE DATE(match_time) = CURDATE()
                """)
                today_matches = (await cursor.fetchone())[0]
                
                # 今天未开赛的比赛
                await cursor.execute("""
                    SELECT COUNT(*) FROM race_matches 
                    WHERE DATE(match_time) = CURDATE() 
                    AND match_time > NOW()
                    AND match_status IN ('未开始', '延期')
                """)
                today_upcoming = (await cursor.fetchone())[0]
                
                # 有比分的比赛数
                await cursor.execute("""
                    SELECT COUNT(*) FROM race_matches 
                    WHERE home_score IS NOT NULL AND away_score IS NOT NULL
                """)
                matches_with_scores = (await cursor.fetchone())[0]
                
                stats_info = []
                stats_info.append("📊 数据库统计信息")
                stats_info.append("=" * 30)
                stats_info.append(f"📈 总比赛数: {total_matches}")
                stats_info.append(f"📅 今天比赛: {today_matches}")
                stats_info.append(f"⏰ 今天未开赛: {today_upcoming}")
                stats_info.append(f"⚽ 有比分比赛: {matches_with_scores}")
                stats_info.append(f"❓ 无比分比赛: {total_matches - matches_with_scores}")
                stats_info.append("")
                stats_info.append("🏆 按状态统计:")
                
                for status, count in status_stats:
                    stats_info.append(f"   {status}: {count}")
                
                return "\n".join(stats_info)
                
    except Exception as e:
        logger.error(f"获取统计信息失败: {e}")
        raise

def getResponse(success, data):
    code = 200
    if not success:
        code = 500
    return jsonify({"code": code, "data": data})


async def start_scheduler():
    """启动定时任务调度器"""
    global scheduler
    try:
        scheduler = SimpleScheduler()
        logger.info("📅 启动定时任务调度器...")
        await scheduler.start(run_immediately=scheduler.run_immediately)
    except Exception as e:
        logger.error(f"❌ 定时任务调度器启动失败: {e}")

def run_scheduler_in_thread():
    """在独立线程中运行调度器"""
    scheduler_loop = asyncio.new_event_loop()
    asyncio.set_event_loop(scheduler_loop)
    try:
        scheduler_loop.run_until_complete(start_scheduler())
    except Exception as e:
        logger.error(f"调度器线程异常: {e}")
    finally:
        scheduler_loop.close()

async def main(loop):
    BrowserRunnerConstants.PIKA_CLIENT = PikaClient(rabbitmq_setting)
    await BrowserRunnerConstants.PIKA_CLIENT.connect(loop)
    print(f'pika_client: {BrowserRunnerConstants.PIKA_CLIENT}')

if __name__ == "__main__":
    # 检查是否启用MCP服务
    enable_mcp = os.environ.get('ENABLE_MCP', 'true').lower() == 'true'
    
    # 检查是否启用定时任务调度器
    enable_scheduler = os.environ.get('ENABLE_SCHEDULER', 'true').lower() == 'true'
    
    loop = asyncio.get_event_loop()
    loop.run_until_complete(main(loop))
    
    if enable_mcp:
        logger.info("✅ MCP服务已集成到Flask应用中")
        logger.info("🔌 MCP接口: GET /mcp/tools, POST /mcp/call")
    
    # 启动定时任务调度器（在独立线程中）
    if enable_scheduler:
        scheduler_thread = threading.Thread(target=run_scheduler_in_thread, daemon=True)
        scheduler_thread.start()
        logger.info("✅ 定时任务调度器已在后台启动")
    
    logger.info("🚀 启动Flask应用 (包含MCP服务和定时任务调度器)")
    
    try:
        app.run(host='0.0.0.0', port=8989, debug=False, threaded=True)
    except KeyboardInterrupt:
        logger.info("收到停止信号，正在关闭应用...")
        if scheduler:
            scheduler.stop()
        logger.info("✅ 应用已停止")
