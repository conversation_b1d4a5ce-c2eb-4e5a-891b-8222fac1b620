import encoding_init  # 必须在最开始导入，自动配置编码
from pathlib import Path
from datetime import datetime, timedelta
import re

from browser_runner import BrowserRunner
from conf import BASE_DIR, mysql_setting
from mysql_client import MySQLClient

import secrets
import string

from logging_config import setup_module_logging

logger = setup_module_logging(__name__)

class ResultDataRunner:
    def __init__(self):
        pid = self.generate_nano_id()
        file_name = pid + ".json"
        self.base_path = Path(BASE_DIR / "data")
        self.data_file = self.base_path / file_name
        self.mysql_client = MySQLClient(mysql_setting)
        self.current_date = None

    async def start(self):
        """开始获取比赛结果并更新数据库"""
        # 连接数据库
        await self.mysql_client.connect()
        
        # 计算日期范围：昨天到今天
        today = datetime.now().date()
        yesterday = today - timedelta(days=1)
        
        start_date = yesterday.strftime('%Y-%m-%d')
        end_date = today.strftime('%Y-%m-%d')
        
        logger.info(f"开始获取比赛结果，日期范围: {start_date} 到 {end_date}")
        
        # 启动浏览器抓取比赛结果
        browser_runner = BrowserRunner(self.process)
        await browser_runner.start()

    async def process(self, page):
        """处理比赛结果页面"""
        await page.goto("https://data.7m.com.cn/result_data/default_big.shtml")
        table = page.locator("#result_tb")
        trs = table.locator("tr")
        tr_all = await trs.all()

        web_matches = []

        for i, tr in enumerate(tr_all):
            try:
                # logger.info(f"📋 处理第{i}行数据")
                
                # 检查是否是日期行
                date_cell = tr.locator("td[colspan]")
                if await date_cell.count() > 0:
                    date_text = await date_cell.inner_text()
                    # logger.info(f"🔍 第{i}行是合并列: '{date_text.strip()}'")
                    
                    if date_text and date_text.strip().startswith('202'):
                        raw_date = date_text.strip()
                        # 转换日期格式为标准YYYY-MM-DD格式
                        self.current_date = self.normalize_date_format(raw_date)
                        # logger.info(f'✅ 识别到日期: {raw_date} -> {self.current_date}')
                        continue
                    else:
                        # logger.info(f"⏭️ 跳过非日期合并列: {date_text.strip()}")
                        continue

                # 解析比赛结果数据行
                tds = tr.locator("td:not(:empty)")
                td_count = await tds.count()

                if td_count >= 5:  # 结果页面至少需要5列数据（联赛、时间、主队、比分、客队）
                    td_texts = []
                    for j in range(td_count):
                        td = tds.nth(j)
                        text = await td.inner_text()
                        td_texts.append(text.strip())

                    # 解析比赛结果数据
                    match_result = self.parse_match_result(td_texts)
                    if match_result:
                        web_matches.append(match_result)

            except Exception as e:
                logger.error(f"❌ 处理第{i}行数据时出错: {e}")
                continue

        logger.info(f"从网页获取到 {len(web_matches)} 场比赛结果")
        
        # 直接更新数据库
        await self.match_and_update_scores(web_matches)

    def normalize_date_format(self, date_str: str) -> str:
        """将各种日期格式标准化为YYYY-MM-DD格式"""
        try:
            date_str = date_str.strip()
            
            # 处理"2025年08月18日"格式
            if re.match(r'\d{4}年\d{1,2}月\d{1,2}日', date_str):
                date_match = re.search(r'(\d{4})年(\d{1,2})月(\d{1,2})日', date_str)
                if date_match:
                    year = date_match.group(1)
                    month = date_match.group(2).zfill(2)
                    day = date_match.group(3).zfill(2)
                    return f"{year}-{month}-{day}"
            
            # 处理"2025-08-18"格式（已经是标准格式）
            if re.match(r'\d{4}-\d{2}-\d{2}', date_str):
                return date_str[:10]  # 取前10位
            
            # 处理"20250818"格式
            if re.match(r'\d{8}', date_str):
                year = date_str[:4]
                month = date_str[4:6]
                day = date_str[6:8]
                return f"{year}-{month}-{day}"
            
            # 如果无法识别，使用当前日期
            # logger.warning(f"无法识别日期格式: {date_str}，使用当前日期")
            return datetime.now().strftime('%Y-%m-%d')
            
        except Exception as e:
            logger.error(f"日期格式标准化失败: {e}, 输入: {date_str}")
            return datetime.now().strftime('%Y-%m-%d')

    def clean_team_name(self, team_name: str) -> str:
        """清理队名中的多余字符"""
        if not team_name:
            return team_name
            
        # 移除常见的多余字符和标记
        cleaned = team_name.strip()
        
        # 移除方括号及其内容 [角标]
        cleaned = re.sub(r'\[.*?\]', '', cleaned)
        
        # 移除圆括号及其内容 (备注)
        cleaned = re.sub(r'\(.*?\)', '', cleaned)
        
        # 移除常见的特殊符号，包括星号
        cleaned = re.sub(r'[★☆※◆◇▲△▼▽■□●○*]', '', cleaned)
        
        # 移除队名前后的星号和空格 (如: "* 泰拿尼斯" -> "泰拿尼斯")
        cleaned = re.sub(r'^\*\s*', '', cleaned)
        cleaned = re.sub(r'\s*\*$', '', cleaned)
        
        # 移除多余的空白字符
        cleaned = re.sub(r'\s+', ' ', cleaned).strip()
        
        # 移除队名前后的数字标记 (如: 1.曼城 -> 曼城)
        cleaned = re.sub(r'^\d+\.?\s*', '', cleaned)
        
        return cleaned

    def parse_match_result(self, td_texts: list) -> dict:
        """解析比赛结果数据"""
        if len(td_texts) < 5:
            return None

        try:
            # 根据结果页面格式: [联赛, 时间, 主队, 比分, 客队]
            league_name = td_texts[0].strip()
            match_time_str = td_texts[1].strip()
            home_team_raw = td_texts[2].strip()
            score_str = td_texts[3].strip()
            away_team_raw = td_texts[4].strip()

            # 清理队名
            home_team = self.clean_team_name(home_team_raw)
            away_team = self.clean_team_name(away_team_raw)

            # 验证必要字段
            if not league_name or not home_team or not away_team or not score_str:
                return None

            # 解析比分
            home_score, away_score = self.parse_score(score_str)
            if home_score is None or away_score is None:
                return None

            # 解析时间
            match_datetime = self.parse_match_time(match_time_str)
            if not match_datetime:
                return None

            # 记录清理前后的队名对比（用于调试）
            if home_team_raw != home_team or away_team_raw != away_team:
                logger.debug(f"队名清理: '{home_team_raw}' -> '{home_team}', '{away_team_raw}' -> '{away_team}'")

            return {
                'league_name': league_name,
                'home_team': home_team,
                'away_team': away_team,
                'match_time': match_datetime,
                'home_score': home_score,
                'away_score': away_score,
                'match_status': '已结束'
            }

        except Exception as e:
            logger.error(f"解析比赛结果数据时出错: {e}, 数据: {td_texts}")
            return None

    def parse_score(self, score_str: str) -> tuple:
        """解析比分字符串"""
        try:
            score_str = score_str.strip()
            
            # 匹配各种比分格式: 2-1, 0:3, 1 - 2, 3：0 等
            score_patterns = [
                r'(\d+)\s*[-:：]\s*(\d+)',  # 2-1, 0:3, 3：0
                r'(\d+)\s+(\d+)',          # 2 1
            ]
            
            for pattern in score_patterns:
                match = re.match(pattern, score_str)
                if match:
                    home_score = int(match.group(1))
                    away_score = int(match.group(2))
                    return home_score, away_score
            
            logger.debug(f"无法解析比分格式: {score_str}")
            return None, None
            
        except Exception as e:
            logger.error(f"解析比分时出错: {e}, 比分字符串: {score_str}")
            return None, None

    def parse_match_time(self, time_str: str) -> datetime:
        """解析比赛时间"""
        try:
            time_str = time_str.strip()
            current_year = datetime.now().year

            # 处理 "08月19日 16:00" 格式
            date_time_pattern = r'(\d{1,2})月(\d{1,2})日\s+(\d{1,2}):(\d{2})'
            match = re.match(date_time_pattern, time_str)
            if match:
                month = int(match.group(1))
                day = int(match.group(2))
                hour = int(match.group(3))
                minute = int(match.group(4))

                try:
                    match_datetime = datetime(current_year, month, day, hour, minute, 0)
                    return match_datetime
                except ValueError as ve:
                    logger.error(f"日期构造失败: {ve}")
                    return None

            # 处理简单的时间格式 "20:00"
            elif re.match(r'\d{1,2}:\d{2}', time_str):
                if not self.current_date:
                    self.current_date = datetime.now().strftime('%Y-%m-%d')

                # 确保current_date是正确的YYYY-MM-DD格式
                date_part = self.current_date
                if not re.match(r'\d{4}-\d{2}-\d{2}', date_part):
                    # 如果current_date不是标准格式，尝试转换
                    if re.match(r'\d{4}年\d{1,2}月\d{1,2}日', date_part):
                        # 转换"2025年08月18日"格式
                        date_match = re.search(r'(\d{4})年(\d{1,2})月(\d{1,2})日', date_part)
                        if date_match:
                            year = date_match.group(1)
                            month = date_match.group(2).zfill(2)
                            day = date_match.group(3).zfill(2)
                            date_part = f"{year}-{month}-{day}"
                    else:
                        # 使用当前日期作为备选
                        date_part = datetime.now().strftime('%Y-%m-%d')
                        logger.warning(f"无法解析日期格式 {self.current_date}，使用当前日期: {date_part}")

                time_part = time_str.strip()
                datetime_str = f"{date_part} {time_part}:00"
                try:
                    return datetime.strptime(datetime_str, '%Y-%m-%d %H:%M:%S')
                except ValueError as ve:
                    logger.error(f"时间解析失败: {ve}, 尝试解析: {datetime_str}")
                    return None

            # 处理纯日期格式 "08月19日"
            elif re.match(r'(\d{1,2})月(\d{1,2})日', time_str):
                date_pattern = r'(\d{1,2})月(\d{1,2})日'
                match = re.match(date_pattern, time_str)
                if match:
                    month = int(match.group(1))
                    day = int(match.group(2))

                    try:
                        match_datetime = datetime(current_year, month, day, 0, 0, 0)
                        return match_datetime
                    except ValueError as ve:
                        logger.error(f"日期构造失败: {ve}")
                        return None

            else:
                logger.debug(f"无法识别的时间格式: {time_str}")
                return None

        except Exception as e:
            logger.error(f"解析时间失败: {e}, 时间字符串: {time_str}")
            return None

    async def match_and_update_scores(self, web_matches: list):
        """直接将网页比分数据更新到数据库"""
        if not web_matches:
            logger.info("没有获取到比赛结果数据")
            return
        
        # 准备UPSERT数据
        upsert_data = []
        for web_match in web_matches:
            upsert_data.append({
                'league_name': web_match['league_name'],
                'home_team': web_match['home_team'],
                'away_team': web_match['away_team'],
                'match_time': web_match['match_time'],
                'home_score': web_match['home_score'],
                'away_score': web_match['away_score'],
                'match_status': web_match['match_status']
            })
        
        # 批量更新到数据库
        result = await self.mysql_client.upsert_matches_batch(upsert_data)
        
        logger.info(f"📊 比分更新完成: 处理 {len(web_matches)} 场比赛，影响 {result['affected']} 行数据")
        
        # 记录每场比赛的更新情况
        for web_match in web_matches:
            logger.info(f"✅ 处理比分: {web_match['league_name']} - {web_match['home_team']} vs {web_match['away_team']} = {web_match['home_score']}-{web_match['away_score']}")

    def generate_nano_id(self, length=10):
        # 定义可用字符集
        alphabet = string.ascii_letters + string.digits
        # 生成指定长度的随机字符串
        nano_id = ''.join(secrets.choice(alphabet) for _ in range(length))
        return nano_id