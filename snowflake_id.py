import time
import threading
from typing import Optional

class SnowflakeIdGenerator:
    """
    雪花算法ID生成器
    类似MyBatis-Plus的ASSIGN_ID策略
    
    ID结构 (64位):
    - 1位符号位 (固定为0)
    - 41位时间戳 (毫秒级，可用69年)
    - 10位机器ID (支持1024个节点)
    - 12位序列号 (每毫秒可生成4096个ID)
    """
    
    def __init__(self, datacenter_id: int = 1, worker_id: int = 1):
        # 各部分位数
        self.WORKER_ID_BITS = 5
        self.DATACENTER_ID_BITS = 5
        self.SEQUENCE_BITS = 12
        
        # 最大值
        self.MAX_WORKER_ID = (1 << self.WORKER_ID_BITS) - 1
        self.MAX_DATACENTER_ID = (1 << self.DATACENTER_ID_BITS) - 1
        self.MAX_SEQUENCE = (1 << self.SEQUENCE_BITS) - 1
        
        # 位移
        self.WORKER_ID_SHIFT = self.SEQUENCE_BITS
        self.DATACENTER_ID_SHIFT = self.SEQUENCE_BITS + self.WORKER_ID_BITS
        self.TIMESTAMP_LEFT_SHIFT = self.SEQUENCE_BITS + self.WORKER_ID_BITS + self.DATACENTER_ID_BITS
        
        # 起始时间戳 (2020-01-01 00:00:00)
        self.EPOCH = 1577836800000
        
        # 验证参数
        if worker_id > self.MAX_WORKER_ID or worker_id < 0:
            raise ValueError(f"Worker ID must be between 0 and {self.MAX_WORKER_ID}")
        if datacenter_id > self.MAX_DATACENTER_ID or datacenter_id < 0:
            raise ValueError(f"Datacenter ID must be between 0 and {self.MAX_DATACENTER_ID}")
        
        self.worker_id = worker_id
        self.datacenter_id = datacenter_id
        self.sequence = 0
        self.last_timestamp = -1
        
        # 线程锁
        self.lock = threading.Lock()
    
    def _current_millis(self) -> int:
        """获取当前毫秒时间戳"""
        return int(time.time() * 1000)
    
    def _wait_next_millis(self, last_timestamp: int) -> int:
        """等待下一毫秒"""
        timestamp = self._current_millis()
        while timestamp <= last_timestamp:
            timestamp = self._current_millis()
        return timestamp
    
    def next_id(self) -> int:
        """生成下一个ID"""
        with self.lock:
            timestamp = self._current_millis()
            
            # 时钟回拨检查
            if timestamp < self.last_timestamp:
                raise RuntimeError(f"Clock moved backwards. Refusing to generate id for {self.last_timestamp - timestamp} milliseconds")
            
            # 同一毫秒内
            if timestamp == self.last_timestamp:
                self.sequence = (self.sequence + 1) & self.MAX_SEQUENCE
                if self.sequence == 0:
                    # 序列号溢出，等待下一毫秒
                    timestamp = self._wait_next_millis(self.last_timestamp)
            else:
                # 新的毫秒，重置序列号
                self.sequence = 0
            
            self.last_timestamp = timestamp
            
            # 组装ID
            snowflake_id = (
                ((timestamp - self.EPOCH) << self.TIMESTAMP_LEFT_SHIFT) |
                (self.datacenter_id << self.DATACENTER_ID_SHIFT) |
                (self.worker_id << self.WORKER_ID_SHIFT) |
                self.sequence
            )
            
            return snowflake_id
    
    def parse_id(self, snowflake_id: int) -> dict:
        """解析雪花ID"""
        timestamp = ((snowflake_id >> self.TIMESTAMP_LEFT_SHIFT) + self.EPOCH)
        datacenter_id = (snowflake_id >> self.DATACENTER_ID_SHIFT) & self.MAX_DATACENTER_ID
        worker_id = (snowflake_id >> self.WORKER_ID_SHIFT) & self.MAX_WORKER_ID
        sequence = snowflake_id & self.MAX_SEQUENCE
        
        return {
            'id': snowflake_id,
            'timestamp': timestamp,
            'datetime': time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(timestamp / 1000)),
            'datacenter_id': datacenter_id,
            'worker_id': worker_id,
            'sequence': sequence
        }


# 全局ID生成器实例
_id_generator: Optional[SnowflakeIdGenerator] = None

def get_id_generator() -> SnowflakeIdGenerator:
    """获取全局ID生成器实例"""
    global _id_generator
    if _id_generator is None:
        _id_generator = SnowflakeIdGenerator(datacenter_id=1, worker_id=1)
    return _id_generator

def generate_id() -> int:
    """生成雪花ID"""
    return get_id_generator().next_id()

def parse_snowflake_id(snowflake_id: int) -> dict:
    """解析雪花ID"""
    return get_id_generator().parse_id(snowflake_id)


if __name__ == "__main__":
    # 测试代码
    generator = SnowflakeIdGenerator()
    
    print("生成10个雪花ID:")
    for i in range(10):
        snowflake_id = generator.next_id()
        parsed = generator.parse_id(snowflake_id)
        print(f"ID: {snowflake_id}, 时间: {parsed['datetime']}, 序列: {parsed['sequence']}")