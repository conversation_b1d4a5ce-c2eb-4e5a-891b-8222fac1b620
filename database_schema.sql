-- 比赛数据表结构
CREATE DATABASE IF NOT EXISTS race_data_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE race_data_db;

-- 比赛数据表
CREATE TABLE IF NOT EXISTS race_matches (
    id BIGINT PRIMARY KEY COMMENT '主键ID(雪花算法)',
    league_name VARCHAR(255) NOT NULL COMMENT '联赛名称',
    home_team VARCHAR(255) NOT NULL COMMENT '主队名称',
    away_team VARCHAR(255) NOT NULL COMMENT '客队名称',
    match_time DATETIME NOT NULL COMMENT '比赛时间',
    home_score INT DEFAULT NULL COMMENT '主队得分',
    away_score INT DEFAULT NULL COMMENT '客队得分',
    match_status ENUM('未开始', '进行中', '已结束', '延期', '取消') DEFAULT '未开始' COMMENT '比赛状态',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_league_name (league_name),
    INDEX idx_match_time (match_time),
    INDEX idx_match_status (match_status),
    UNIQUE KEY unique_match (league_name, home_team, away_team, match_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='比赛数据表';
