#!/usr/bin/env python3
"""
编码初始化模块
在其他模块导入前自动配置UTF-8编码
"""
import os
import sys


def _init_encoding():
    """初始化编码设置"""
    # 设置环境变量
    os.environ['PYTHONIOENCODING'] = 'utf-8'
    os.environ['PYTHONUTF8'] = '1'
    
    # 尝试重新配置标准输出
    try:
        if hasattr(sys.stdout, 'reconfigure'):
            sys.stdout.reconfigure(encoding='utf-8', errors='replace')
        if hasattr(sys.stderr, 'reconfigure'):
            sys.stderr.reconfigure(encoding='utf-8', errors='replace')
    except:
        pass


# 模块导入时自动执行
_init_encoding()
