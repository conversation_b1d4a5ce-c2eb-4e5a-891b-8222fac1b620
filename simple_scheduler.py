import asyncio
from datetime import datetime, timedelta
from race_data_runner import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from result_data_runner import ResultDataR<PERSON><PERSON>
from team_analysis_runner import TeamAnalysisRunner
from logging_config import setup_module_logging
from conf import BASE_DIR
import yaml

from titan007_race_runner import Titan007RaceRunner
from yaml_utils import setup_yaml_parser

logger = setup_module_logging(__name__)

class SimpleScheduler:
    """简单的定时任务调度器"""
    
    def __init__(self):
        # 从dev.yml读取配置
        config_path = BASE_DIR / "dev.yml"
        setup_yaml_parser()
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        scheduler_config = config.get('scheduler', {})
        self.race_data_interval = int(scheduler_config.get('race_data_interval_minutes', 10))
        self.result_data_interval = int(scheduler_config.get('result_data_interval_minutes', 5))
        self.team_analysis_interval = int(scheduler_config.get('team_analysis_interval_minutes', 30))
        self.run_immediately = str(scheduler_config.get('run_immediately', 'false')).lower() == 'true'
        
        # 检查任务是否启用
        self.race_data_enabled = self.race_data_interval > 0
        self.result_data_enabled = self.result_data_interval > 0
        self.team_analysis_enabled = self.team_analysis_interval > 0
        
        if self.race_data_enabled:
            logger.info(f"比赛数据抓取间隔: {self.race_data_interval} 分钟")
        else:
            logger.info("比赛数据抓取任务已禁用 (间隔时间为0)")
            
        if self.result_data_enabled:
            logger.info(f"比分更新间隔: {self.result_data_interval} 分钟")
        else:
            logger.info("比分更新任务已禁用 (间隔时间为0)")
            
        if self.team_analysis_enabled:
            logger.info(f"队伍分析数据间隔: {self.team_analysis_interval} 分钟")
        else:
            logger.info("队伍分析数据任务已禁用 (间隔时间为0)")
        
        self.running = False
        self.last_race_run = None
        self.last_result_run = None
        self.last_team_analysis_run = None
    
    async def fetch_race_data_job(self):
        """比赛数据抓取任务"""
        try:
            logger.info("🚀 开始执行比赛数据抓取任务")
            start_time = datetime.now()
            
            race_runner = Titan007RaceRunner()
            await race_runner.start()
            
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            logger.info(f"✅ 比赛数据抓取任务完成，耗时: {duration:.2f}秒")
            
            self.last_race_run = datetime.now()
            
        except Exception as e:
            logger.error(f"❌ 比赛数据抓取任务失败: {e}")
            import traceback
            logger.error(f"错误详情: {traceback.format_exc()}")
    
    async def update_result_data_job(self):
        """比分更新任务"""
        try:
            logger.info("🔄 开始执行比分更新任务")
            start_time = datetime.now()
            
            result_runner = ResultDataRunner()
            await result_runner.start()
            
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            logger.info(f"✅ 比分更新任务完成，耗时: {duration:.2f}秒")
            
            self.last_result_run = datetime.now()
            
        except Exception as e:
            logger.error(f"❌ 比分更新任务失败: {e}")
            import traceback
            logger.error(f"错误详情: {traceback.format_exc()}")
    
    async def team_analysis_data_job(self):
        """队伍分析数据任务"""
        try:
            logger.info("📊 开始执行队伍分析数据任务")
            start_time = datetime.now()
            
            team_analysis_runner = TeamAnalysisRunner()
            await team_analysis_runner.start()
            await team_analysis_runner.close()
            
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            logger.info(f"✅ 队伍分析数据任务完成，耗时: {duration:.2f}秒")
            
            self.last_team_analysis_run = datetime.now()
            
        except Exception as e:
            logger.error(f"❌ 队伍分析数据任务失败: {e}")
            import traceback
            logger.error(f"错误详情: {traceback.format_exc()}")
    
    def should_run_race_task(self) -> bool:
        """判断是否应该执行比赛数据抓取任务"""
        # 如果任务被禁用，不执行
        if not self.race_data_enabled:
            return False
            
        if self.last_race_run is None:
            return True
        
        elapsed = datetime.now() - self.last_race_run
        return elapsed >= timedelta(minutes=self.race_data_interval)
    
    def should_run_result_task(self) -> bool:
        """判断是否应该执行比分更新任务"""
        # 如果任务被禁用，不执行
        if not self.result_data_enabled:
            return False
            
        if self.last_result_run is None:
            return True
        
        elapsed = datetime.now() - self.last_result_run
        return elapsed >= timedelta(minutes=self.result_data_interval)
    
    def should_run_team_analysis_task(self) -> bool:
        """判断是否应该执行队伍分析数据任务"""
        # 如果任务被禁用，不执行
        if not self.team_analysis_enabled:
            return False
            
        if self.last_team_analysis_run is None:
            return True
        
        elapsed = datetime.now() - self.last_team_analysis_run
        return elapsed >= timedelta(minutes=self.team_analysis_interval)
    
    def get_next_run_times(self) -> dict:
        """获取下次执行时间"""
        now = datetime.now()
        
        # 比赛数据抓取下次执行时间
        if self.race_data_enabled:
            if self.last_race_run:
                next_race = self.last_race_run + timedelta(minutes=self.race_data_interval)
            else:
                next_race = now
        else:
            next_race = None
        
        # 比分更新下次执行时间
        if self.result_data_enabled:
            if self.last_result_run:
                next_result = self.last_result_run + timedelta(minutes=self.result_data_interval)
            else:
                next_result = now
        else:
            next_result = None
        
        # 队伍分析数据下次执行时间
        if self.team_analysis_enabled:
            if self.last_team_analysis_run:
                next_team_analysis = self.last_team_analysis_run + timedelta(minutes=self.team_analysis_interval)
            else:
                next_team_analysis = now
        else:
            next_team_analysis = None
        
        return {
            'race_data': next_race,
            'result_data': next_result,
            'team_analysis': next_team_analysis
        }
    
    async def start(self, run_immediately: bool = False):
        """启动调度器"""
        self.running = True
        logger.info("📅 简单定时任务调度器启动成功")
        
        # 立即执行一次（如果需要）
        if run_immediately:
            logger.info("🚀 立即执行一次数据抓取任务")
            if self.race_data_enabled:
                await self.fetch_race_data_job()
            else:
                logger.info("⚠️ 比赛数据抓取任务已禁用，跳过立即执行")
        
        # 显示下次执行时间
        next_times = self.get_next_run_times()
        if next_times['race_data']:
            logger.info(f"📋 比赛数据抓取下次执行: {next_times['race_data'].strftime('%Y-%m-%d %H:%M:%S')}")
        else:
            logger.info("📋 比赛数据抓取任务已禁用")
            
        if next_times['result_data']:
            logger.info(f"📋 比分更新下次执行: {next_times['result_data'].strftime('%Y-%m-%d %H:%M:%S')}")
        else:
            logger.info("📋 比分更新任务已禁用")
            
        if next_times['team_analysis']:
            logger.info(f"📋 队伍分析数据下次执行: {next_times['team_analysis'].strftime('%Y-%m-%d %H:%M:%S')}")
        else:
            logger.info("📋 队伍分析数据任务已禁用")
        
        try:
            while self.running:
                # 检查是否需要执行比赛数据抓取
                if self.should_run_race_task():
                    await self.fetch_race_data_job()
                
                # 检查是否需要执行比分更新
                # if self.should_run_result_task():
                #     await self.update_result_data_job()
                
                # 检查是否需要执行队伍分析数据任务
                # if self.should_run_team_analysis_task():
                #     await self.team_analysis_data_job()
                
                # 每分钟检查一次
                await asyncio.sleep(60)
                
        except KeyboardInterrupt:
            logger.info("收到停止信号，正在关闭调度器...")
        except Exception as e:
            logger.error(f"调度器运行出错: {e}")
        finally:
            self.running = False
    
    def stop(self):
        """停止调度器"""
        self.running = False
        logger.info("📅 简单定时任务调度器已停止")
    
    def get_status(self) -> dict:
        """获取调度器状态"""
        next_times = self.get_next_run_times()
        
        return {
            'running': self.running,
            'race_data_enabled': self.race_data_enabled,
            'result_data_enabled': self.result_data_enabled,
            'team_analysis_enabled': self.team_analysis_enabled,
            'race_data_interval': self.race_data_interval,
            'result_data_interval': self.result_data_interval,
            'team_analysis_interval': self.team_analysis_interval,
            'last_race_run': self.last_race_run.strftime('%Y-%m-%d %H:%M:%S') if self.last_race_run else None,
            'last_result_run': self.last_result_run.strftime('%Y-%m-%d %H:%M:%S') if self.last_result_run else None,
            'last_team_analysis_run': self.last_team_analysis_run.strftime('%Y-%m-%d %H:%M:%S') if self.last_team_analysis_run else None,
            'next_race_run': next_times['race_data'].strftime('%Y-%m-%d %H:%M:%S') if next_times['race_data'] else '已禁用',
            'next_result_run': next_times['result_data'].strftime('%Y-%m-%d %H:%M:%S') if next_times['result_data'] else '已禁用',
            'next_team_analysis_run': next_times['team_analysis'].strftime('%Y-%m-%d %H:%M:%S') if next_times['team_analysis'] else '已禁用'
        }

async def main():
    """主函数"""
    scheduler = SimpleScheduler()
    
    print("🚀 启动简单定时任务调度器")
    
    if scheduler.race_data_enabled:
        print(f"📅 比赛数据抓取间隔: {scheduler.race_data_interval} 分钟")
    else:
        print("📅 比赛数据抓取任务: 已禁用 (间隔时间为0)")
        
    if scheduler.result_data_enabled:
        print(f"📅 比分更新间隔: {scheduler.result_data_interval} 分钟")
    else:
        print("📅 比分更新任务: 已禁用 (间隔时间为0)")
        
    if scheduler.team_analysis_enabled:
        print(f"📅 队伍分析数据间隔: {scheduler.team_analysis_interval} 分钟")
    else:
        print("📅 队伍分析数据任务: 已禁用 (间隔时间为0)")
        
    print(f"🏃 立即执行: {'是' if scheduler.run_immediately else '否'}")
    print("-" * 50)
    
    try:
        await scheduler.start(run_immediately=scheduler.run_immediately)
    except KeyboardInterrupt:
        print("\n🛑 收到停止信号，正在关闭调度器...")
    finally:
        scheduler.stop()
        print("✅ 调度器已停止")

if __name__ == "__main__":
    asyncio.run(main())