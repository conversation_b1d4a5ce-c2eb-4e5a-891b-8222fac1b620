"""
MySQL客户端扩展方法 - titan007_race表操作
"""

async def upsert_titan007_race_batch(self, matches_data):
    """
    批量UPSERT titan007_race表数据（有则更新，无则插入）
    
    Args:
        matches_data: 比赛数据列表，每个元素包含比赛信息
        
    Returns:
        dict: 包含处理结果的字典 {'total': 总数, 'affected': 影响行数}
    """
    if not matches_data:
        return {'total': 0, 'affected': 0}
    
    # 过滤掉没有race_id的数据
    valid_matches = [match for match in matches_data if match.get('race_id')]
    
    if not valid_matches:
        return {'total': 0, 'affected': 0}
    
    # 构建UPSERT SQL语句
    # 注意：race_id现在是主键，如果已存在则只更新score和status字段
    upsert_sql = """
    INSERT INTO titan007_race (
        race_id, league_name, home_team, away_team, match_time,
        home_score, away_score, match_status, pan, analysis_id
    ) VALUES (
        %(race_id)s, %(league_name)s, %(home_team)s, %(away_team)s, %(match_time)s,
        %(home_score)s, %(away_score)s, %(match_status)s, %(pan)s, %(analysis_id)s
    )
    ON DUPLICATE KEY UPDATE
        home_score = VALUES(home_score),
        away_score = VALUES(away_score),
        match_status = VALUES(match_status),
        updated_at = CURRENT_TIMESTAMP
    """
    
    try:
        # 执行批量UPSERT
        affected_rows = await self.execute_many(upsert_sql, valid_matches)
        
        return {
            'total': len(valid_matches),
            'affected': affected_rows
        }
        
    except Exception as e:
        self.logger.error(f"批量UPSERT titan007_race数据失败: {e}")
        raise e


# 将方法添加到MySQLClient类中
def add_titan007_methods_to_mysql_client():
    """将titan007相关方法添加到MySQLClient类中"""
    from mysql_client import MySQLClient
    
    # 动态添加方法到MySQLClient类
    MySQLClient.upsert_titan007_race_batch = upsert_titan007_race_batch