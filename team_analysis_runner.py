import encoding_init  # 必须在最开始导入，自动配置编码
import asyncio
import json
import re
from datetime import datetime, date
from typing import List, Dict, Any, Optional

from browser_runner import BrowserRunner
from mysql_client import MySQLClient
from conf import mysql_setting
from logging_config import setup_module_logging
from snowflake_id import generate_id

logger = setup_module_logging(__name__)

class TeamAnalysisRunner:
    def __init__(self):
        self.mysql_client = MySQLClient(mysql_setting)

    async def start(self):
        """启动队伍分析数据获取任务"""
        # 连接数据库
        await self.mysql_client.connect()

        # 获取符合条件的未结束比赛（关联team_analysis表过滤）
        unfinished_matches = await self.get_unfinished_matches()

        if not unfinished_matches:
            logger.info("没有找到需要处理的比赛数据")
            await self.close()
            return

        logger.info(f"找到 {len(unfinished_matches)} 场需要处理的比赛，开始获取分析数据")

        # 启动浏览器处理分析数据
        browser_runner = BrowserRunner(self.parse_analysis_page)
        await browser_runner.start()

        # 关闭数据库连接
        await self.close()

    async def get_unfinished_matches(self):
        """获取未结束的比赛，关联team_analysis表过滤已存在记录，且比赛在当前日期后"""
        try:
            async with self.mysql_client.pool.acquire() as conn:
                async with conn.cursor() as cursor:
                    # 修改SQL：关联team_analysis表，只选择不存在对应记录且比赛在当前日期后的场次
                    sql = """
                    SELECT rm.id, rm.league_name, rm.home_team, rm.away_team, rm.match_time, 
                           rm.match_status, rm.analysis_id
                    FROM race_matches rm
                    LEFT JOIN team_analysis ta 
                        ON rm.analysis_id = ta.last_analysis_id
                    WHERE rm.match_status IN ('未开始', '进行中') 
                      AND rm.analysis_id IS NOT NULL 
                      AND rm.analysis_id != ''
                      AND ta.id IS NULL  -- 只选择team_analysis中不存在的记录
                      AND rm.match_time > CURDATE()  -- 比赛时间在当前日期之后
                    ORDER BY rm.match_time ASC
                    """

                    await cursor.execute(sql)
                    results = await cursor.fetchall()

                    matches = []
                    for row in results:
                        matches.append({
                            'id': row[0],
                            'league_name': row[1],
                            'home_team': row[2],
                            'away_team': row[3],
                            'match_time': row[4],
                            'match_status': row[5],
                            'analysis_id': row[6]
                        })

                    return matches

        except Exception as e:
            logger.error(f"获取未结束比赛失败: {e}")
            return []

    async def parse_analysis_page(self, page):
        """处理分析数据并保存到数据库"""
        unfinished_matches = await self.get_unfinished_matches()

        # 按analysis_id去重，避免重复访问同一个分析页面
        unique_analysis_ids = {}
        for match in unfinished_matches:
            analysis_id = match['analysis_id']
            if analysis_id not in unique_analysis_ids:
                unique_analysis_ids[analysis_id] = []
            unique_analysis_ids[analysis_id].append(match)

        logger.info(f"需要处理 {len(unique_analysis_ids)} 个不同的分析页面")

        for analysis_id, matches in unique_analysis_ids.items():
            try:
                # 初始化数据结构
                analysis_data = {
                    'home_team_data': {
                        '以往赛绩': None,
                        '统计数据': None,
                        '进球数单双统计': None,
                        '进球数统计': None,
                        '未来赛程': None
                    },
                    'away_team_data': {
                        '以往赛绩': None,
                        '统计数据': None,
                        '进球数单双统计': None,
                        '进球数统计': None,
                        '未来赛程': None
                    },
                    'title': None
                }

                analysis_url = f"https://analyse.7m.com.cn/{analysis_id}/index.shtml"
                logger.info(f"正在处理分析页面: {analysis_url}")

                # 访问分析页面
                await page.goto(analysis_url)

                # 等待页面加载完成
                try:
                    await page.wait_for_selector('table', timeout=30000)
                    logger.info("页面表格元素已加载")
                except Exception as e:
                    logger.error(f"等待表格元素超时: {e}")
                    continue

                # 获取页面标题
                try:
                    title = await page.title()
                    analysis_data['title'] = title
                    logger.info(f"页面标题: {title}")
                except Exception as e:
                    logger.warning(f"获取页面标题失败: {e}")

                # 获取所有表格
                tables = page.locator('table')
                tables_count = await tables.count()
                logger.info(f"找到 {tables_count} 个表格元素")

                if tables_count == 0:
                    logger.warning("未找到任何表格元素")
                    continue

                # 解析关键表格数据
                table_map = {
                    1: {'team': 'home', 'type': '以往赛绩'},
                    2: {'team': 'home', 'type': '统计数据'},
                    5: {'team': 'home', 'type': '进球数单双统计'},
                    6: {'team': 'home', 'type': '进球数统计'},
                    8: {'team': 'home', 'type': '未来赛程'},
                    9: {'team': 'away', 'type': '以往赛绩'},
                    10: {'team': 'away', 'type': '统计数据'},
                    13: {'team': 'away', 'type': '进球数单双统计'},
                    14: {'team': 'away', 'type': '进球数统计'},
                    16: {'team': 'away', 'type': '未来赛程'}
                }

                # 解析指定index的表格
                for table_idx in table_map.keys():
                    if table_idx >= tables_count:
                        logger.warning(f"表格index {table_idx} 不存在，跳过")
                        continue

                    table = tables.nth(table_idx)
                    table_data = await self.parse_table_data(table)
                    if not table_data:
                        continue

                    config = table_map[table_idx]
                    team_key = f"{config['team']}_team_data"
                    data_type = config['type']

                    if data_type == '统计数据':
                        parsed_data = await self.parse_statistics_table(table_data)
                        analysis_data[team_key][data_type] = parsed_data
                    else:
                        analysis_data[team_key][data_type] = table_data

                # 保存数据到数据库（按球队去重）
                processed_teams = set()
                for match in matches:
                    league_name = match['league_name']
                    home_team = match['home_team']
                    away_team = match['away_team']

                    # 保存主队数据
                    if home_team not in processed_teams:
                        await self.save_team_analysis_data(
                            home_team,
                            league_name,
                            analysis_data['home_team_data'],
                            analysis_id,
                            analysis_url
                        )
                        processed_teams.add(home_team)

                    # 保存客队数据
                    if away_team not in processed_teams:
                        await self.save_team_analysis_data(
                            away_team,
                            league_name,
                            analysis_data['away_team_data'],
                            analysis_id,
                            analysis_url
                        )
                        processed_teams.add(away_team)

            except Exception as e:
                logger.error(f"解析分析页面失败: {e}", exc_info=True)
                continue

    async def parse_table_data(self, table):
        """解析表格原始数据"""
        try:
            table_data = {'headers': [], 'rows': []}

            # 获取表头
            headers = table.locator('th')
            headers_count = await headers.count()

            if headers_count > 0:
                for i in range(headers_count):
                    header_text = await headers.nth(i).inner_text()
                    header_text = re.sub(r'[\n\u3000\xa0]', ' ', header_text).strip()
                    table_data['headers'].append(header_text)
            else:
                first_row = table.locator('tr').nth(0)
                cells = first_row.locator('td')
                cells_count = await cells.count()
                if cells_count > 0:
                    for i in range(cells_count):
                        cell_text = await cells.nth(i).inner_text()
                        cell_text = re.sub(r'[\n\u3000\xa0]', ' ', cell_text).strip()
                        table_data['headers'].append(cell_text)
                    start_row = 1
                else:
                    start_row = 0

            # 获取数据行
            rows = table.locator('tr')
            rows_count = await rows.count()

            for i in range(start_row, rows_count):
                row = rows.nth(i)
                cells = row.locator('td')
                cells_count = await cells.count()

                if cells_count > 0:
                    row_data = []
                    for j in range(cells_count):
                        cell_text = await cells.nth(j).inner_text()
                        cell_text = re.sub(r'[\n\u3000\xa0]', ' ', cell_text).strip()
                        row_data.append(cell_text)

                    if any(cell for cell in row_data if cell):
                        table_data['rows'].append(row_data)

            return table_data if (table_data['headers'] or table_data['rows']) else None

        except Exception as e:
            logger.error(f"解析表格数据失败: {e}", exc_info=True)
            return None

    async def parse_statistics_table(self, table_data):
        """解析统计表格数据"""
        try:
            stats = {}
            headers = table_data.get('headers', [])
            rows = table_data.get('rows', [])

            if not headers or not rows:
                return None

            headers_text = ' '.join(headers).lower()

            if any(keyword in headers_text for keyword in ['總勝', '總平', '總負']):
                stats['交锋记录'] = self.extract_match_records_stats(rows, headers)
            elif any(keyword in headers_text for keyword in ['主場', '客場']):
                stats['主客场统计'] = self.extract_home_away_stats(rows, headers)

            return stats if stats else None

        except Exception as e:
            logger.error(f"解析统计表格失败: {e}", exc_info=True)
            return None

    def extract_match_records_stats(self, rows, headers):
        """提取交锋记录统计数据"""
        stats = {"总场次": 0, "胜场": 0, "平场": 0, "负场": 0,
                 "胜场百分比": "0.00%", "平场百分比": "0.00%", "负场百分比": "0.00%"}

        try:
            win_idx = None
            draw_idx = None
            loss_idx = None

            for i, header in enumerate(headers):
                if '總勝' in header:
                    win_idx = i
                elif '總平' in header:
                    draw_idx = i
                elif '總負' in header:
                    loss_idx = i

            if win_idx is not None and draw_idx is not None and loss_idx is not None and rows:
                nums_row = rows[0]
                stats["胜场"] = int(nums_row[win_idx]) if nums_row[win_idx].isdigit() else 0
                stats["平场"] = int(nums_row[draw_idx]) if nums_row[draw_idx].isdigit() else 0
                stats["负场"] = int(nums_row[loss_idx]) if nums_row[loss_idx].isdigit() else 0
                stats["总场次"] = stats["胜场"] + stats["平场"] + stats["负场"]

                if len(rows) > 1:
                    percent_row = rows[1]
                    stats["胜场百分比"] = percent_row[win_idx] if win_idx < len(percent_row) else "0.00%"
                    stats["平场百分比"] = percent_row[draw_idx] if draw_idx < len(percent_row) else "0.00%"
                    stats["负场百分比"] = percent_row[loss_idx] if loss_idx < len(percent_row) else "0.00%"

            return stats
        except Exception as e:
            logger.error(f"提取交锋记录失败: {e}", exc_info=True)
            return stats

    def extract_home_away_stats(self, rows, headers):
        """提取主客场统计数据"""
        stats = {
            "主场": {"胜场": 0, "平场": 0, "负场": 0, "胜场百分比": "0.00%", "平场百分比": "0.00%", "负场百分比": "0.00%"},
            "客场": {"胜场": 0, "平场": 0, "负场": 0, "胜场百分比": "0.00%", "平场百分比": "0.00%", "负场百分比": "0.00%"}
        }

        try:
            for row in rows:
                row_text = ' '.join(row).lower()
                nums = re.findall(r'\d+', row_text)

                if len(nums) >= 3:
                    if '主場' in row_text:
                        stats["主场"]["胜场"] = int(nums[0])
                        stats["主场"]["平场"] = int(nums[1])
                        stats["主场"]["负场"] = int(nums[2])
                        total = sum(stats["主场"].values())
                        if total > 0:
                            stats["主场"]["胜场百分比"] = f"{(stats['主场']['胜场']/total*100):.2f}%"
                            stats["主场"]["平场百分比"] = f"{(stats['主场']['平场']/total*100):.2f}%"
                            stats["主场"]["负场百分比"] = f"{(stats['主场']['负场']/total*100):.2f}%"
                    elif '客場' in row_text:
                        stats["客场"]["胜场"] = int(nums[0])
                        stats["客场"]["平场"] = int(nums[1])
                        stats["客场"]["负场"] = int(nums[2])
                        total = sum(stats["客场"].values())
                        if total > 0:
                            stats["客场"]["胜场百分比"] = f"{(stats['客场']['胜场']/total*100):.2f}%"
                            stats["客场"]["平场百分比"] = f"{(stats['客场']['平场']/total*100):.2f}%"
                            stats["客场"]["负场百分比"] = f"{(stats['客场']['负场']/total*100):.2f}%"

            return stats
        except Exception as e:
            logger.error(f"提取主客场统计失败: {e}", exc_info=True)
            return stats

    async def save_team_analysis_data(self, team_name, league_name, analysis_data, analysis_id, analysis_url):
        """保存队伍分析数据到team_analysis表"""
        try:
            async with self.mysql_client.pool.acquire() as conn:
                async with conn.cursor() as cursor:
                    # 检查是否已存在该队伍的记录（双重检查，防止并发问题）
                    check_sql = "SELECT id FROM team_analysis WHERE team_name = %s AND league_name = %s"
                    await cursor.execute(check_sql, (team_name, league_name))
                    existing = await cursor.fetchone()

                    # 将分析数据序列化为JSON
                    analysis_json = json.dumps(analysis_data, ensure_ascii=False)

                    if existing:
                        # 更新现有记录
                        update_sql = """
                        UPDATE team_analysis 
                        SET analysis_data = %s, 
                            last_analysis_id = %s, 
                            last_analysis_url = %s, 
                            updated_at = CURRENT_TIMESTAMP
                        WHERE team_name = %s AND league_name = %s
                        """
                        await cursor.execute(update_sql, (
                            analysis_json, analysis_id, analysis_url, team_name, league_name
                        ))
                        logger.info(f"✅ 更新队伍分析数据: {team_name} ({league_name})")
                    else:
                        # 插入新记录
                        insert_sql = """
                        INSERT INTO team_analysis 
                        (id, team_name, league_name, analysis_data, last_analysis_id, last_analysis_url)
                        VALUES (%s, %s, %s, %s, %s, %s)
                        """
                        new_id = generate_id()
                        await cursor.execute(insert_sql, (
                            new_id, team_name, league_name, analysis_json, analysis_id, analysis_url
                        ))
                        logger.info(f"✅ 新增队伍分析数据: {team_name} ({league_name})")

                    await conn.commit()

        except Exception as e:
            logger.error(f"保存队伍分析数据失败: {e}", exc_info=True)
            raise

    async def close(self):
        """关闭数据库连接"""
        if self.mysql_client:
            await self.mysql_client.close()
            logger.info("数据库连接已关闭")

# 执行入口
if __name__ == "__main__":
    try:
        runner = TeamAnalysisRunner()
        asyncio.run(runner.start())
    except Exception as e:
        logger.error(f"程序运行失败: {e}", exc_info=True)