-- 队伍分析数据表
CREATE TABLE IF NOT EXISTS team_analysis (
    id BIGINT PRIMARY KEY COMMENT '主键ID(雪花算法)',
    team_name VARCHAR(255) NOT NULL COMMENT '队伍名称',
    league_name VARCHAR(255) NOT NULL COMMENT '联赛名称',
    analysis_data JSON COMMENT '队伍分析数据JSON',
    last_analysis_id VARCHAR(20) COMMENT '最后分析的页面ID',
    last_analysis_url VARCHAR(255) COMMENT '最后分析的页面URL',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_team_league (team_name, league_name),
    INDEX idx_team_name (team_name),
    INDEX idx_league_name (league_name),
    INDEX idx_last_analysis_id (last_analysis_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='队伍分析数据表';
-- 迁移已存在表，去除AUTO_INCREMENT以使用雪花ID
ALTER TABLE team_analysis 
MODIFY COLUMN id BIGINT NOT NULL COMMENT '主键ID(雪花算法)';

-- 为race_matches表添加analysis_id字段（如果不存在）
ALTER TABLE race_matches 
ADD COLUMN IF NOT EXISTS analysis_id VARCHAR(20) DEFAULT NULL COMMENT '分析页面ID（去除bh前缀后的值）' AFTER match_status,
ADD INDEX IF NOT EXISTS idx_analysis_id (analysis_id);
