import asyncio
import json
import os
import time
import uuid
from pathlib import Path
from browser_runner_constants import BrowserRunnerConstants
from conf import BASE_DIR
from playwright.async_api import Playwright, async_playwright
import os
import logging

logger = logging.getLogger()


class BrowserRunner:
    def __init__(self, on_open_success):
        self.context = None
        self.page = None
        self.state = BrowserRunnerConstants.STATE_OFF
        self.on_open_success = on_open_success

    async def start(self):
        if self.state == BrowserRunnerConstants.STATE_OFF:
            self.state = BrowserRunnerConstants.STATE_WAITING_QRCODE
            await self.goto_url()

    async def goto_url(self):
        async with async_playwright() as playwright:
            options = {
                'headless': True
            }
            # Make sure to run headed.
            browser = await playwright.chromium.launch(**options)
            # Setup context however you like.
            context = await browser.new_context()  # Pass any options
            # Pause the page, and start recording manually.
            self.page = await context.new_page()
            await self.on_open_success(self.page)
            await self.page.close()
