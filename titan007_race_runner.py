from pathlib import Path
from datetime import datetime
import re

from browser_runner import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from conf import BASE_DIR, mysql_setting
from mysql_client import MySQLClient

import secrets
import string

from logging_config import setup_module_logging

logger = setup_module_logging(__name__)

class Titan007RaceRunner:
    def __init__(self):
        pid = self.generate_nano_id()
        file_name = pid + ".json"
        self.base_path = Path(BASE_DIR / "data")
        self.data_file = self.base_path / file_name
        self.mysql_client = MySQLClient(mysql_setting)
        self.current_date = None
        self.last_match_hour = None  # 用于跟踪上一场比赛的小时，检测跨天

    async def start(self):
        # 连接数据库
        await self.mysql_client.connect()
        browser_runner = BrowserRunner(self.process)
        await browser_runner.start()

    async def process(self, page):
        # 修改目标URL
        await page.goto("https://live.titan007.com/index2in1_big.aspx?id=1")

        # 先点击button7按钮
        try:
            await page.wait_for_selector("#button7", timeout=5000)
            await page.click("#button7")
            logger.info("成功点击button7按钮")
            # 等待一下让页面响应
            await page.wait_for_timeout(10000)
        except Exception as e:
            logger.error(f"点击button7按钮失败: {e}")

        # 等待异步表格加载完成（最长等待10秒）
        try:
            await page.wait_for_selector("#table_live", timeout=10000)
            logger.info("表格#table_live已成功加载")
        except Exception as e:
            logger.error(f"等待表格加载超时: {e}")
            return

        # 获取表格元素
        table = page.locator("#table_live")
        trs = table.locator("tr")
        tr_all = await trs.all()

        matches_to_insert = []
        current_date = None

        logger.info(f"从#table_live获取到 {len(tr_all)} 行数据")

        for i, tr in enumerate(tr_all):
            try:
                # 跳过第一行表头
                if i == 0:
                    logger.debug("跳过表头行")
                    continue

                # 检查tr的style属性，如果包含display:none则跳过
                tr_style = await tr.get_attribute("style")
                if tr_style and "display:none" in tr_style:
                    logger.debug(f"跳过隐藏行 (display:none): 第{i}行")
                    continue

                # 获取tr的id属性
                tr_id = await tr.get_attribute("id")

                tds = tr.locator("td")
                td_count = await tds.count()

                # 检查是否是合并列的行（可能是日期或提示信息）
                colspan_cell = tr.locator("td[colspan]")
                if await colspan_cell.count() > 0:
                    colspan_text = await colspan_cell.inner_text()
                    colspan_text = colspan_text.strip()

                    # 判断是否是日期行（包含年月日信息）
                    if re.match(r'.*\\d{4}.*\\d{1,2}.*\\d{1,2}.*', colspan_text) or re.match(r'.*\\d{1,2}月\\d{1,2}日.*', colspan_text):
                        current_date = self.extract_date_from_text(colspan_text)
                        # 重置上一场比赛时间，因为遇到了新的日期行
                        self.last_match_hour = None
                        logger.debug(f"遇到新日期行: {current_date}，重置last_match_hour")
                        continue
                    else:
                        # 其他合并列信息（如比赛提示），跳过
                        continue

                # 解析正常的比赛数据行
                if td_count >= 5:  # 根据新表格调整最小列数要求
                    td_texts = []
                    for j in range(td_count):
                        td = tds.nth(j)
                        text = await td.inner_text()
                        td_texts.append(text.strip())

                    # 提取race_id（从analysis链接中获取）
                    race_id = None
                    try:
                        # 查找包含analysis函数调用的链接
                        analysis_link = tr.locator('a[onclick*="analysis("]')
                        if await analysis_link.count() > 0:
                            onclick_attr = await analysis_link.first.get_attribute("onclick")
                            if onclick_attr:
                                # 使用正则表达式提取analysis函数中的数字
                                race_match = re.search(r'analysis\((\d+)\)', onclick_attr)
                                if race_match:
                                    race_id = race_match.group(1)
                                    logger.debug(f"提取到race_id: {race_id}")
                    except Exception as e:
                        logger.debug(f"提取race_id失败: {e}")

                    # 过滤空行
                    if any(text for text in td_texts if text):
                        # 解析比赛数据，传入当前日期和上一场比赛时间
                        match_data = self.parse_table_live_data(td_texts, current_date)
                        if match_data:
                            # 提取analysis_id（去除bh前缀）
                            analysis_id = None
                            if tr_id and tr_id.startswith('bh'):
                                analysis_id = tr_id[2:]  # 去除前两个字符"bh"

                            match_data['analysis_id'] = analysis_id
                            match_data['race_id'] = race_id  # 添加race_id字段
                            matches_to_insert.append(match_data)

            except Exception as e:
                logger.error(f"❌ 处理第{i}行数据时出错: {e}")
                continue

        # 批量UPSERT数据库到titan007_race表（有则更新，无则插入）
        if matches_to_insert:
            result = await self.mysql_client.upsert_titan007_race_batch(matches_to_insert)
            logger.info(f"成功处理 {result['total']} 条titan007比赛数据，影响 {result['affected']} 行")
        else:
            logger.warning("未解析到任何有效的比赛数据")


    def extract_date_from_text(self, text: str) -> str:
        """从文本中提取日期，如果识别的日期大于当前日期3天，则忽略该日期"""
        try:
            extracted_date = None

            # 匹配 "2024年8月19日" 格式
            date_match = re.search(r'(\\d{4})年(\\d{1,2})月(\\d{1,2})日', text)
            if date_match:
                year = date_match.group(1)
                month = date_match.group(2).zfill(2)
                day = date_match.group(3).zfill(2)
                extracted_date = f"{year}-{month}-{day}"

            # 匹配 "8月19日" 格式
            if not extracted_date:
                date_match = re.search(r'(\\d{1,2})月(\\d{1,2})日', text)
                if date_match:
                    current_year = datetime.now().year
                    month = date_match.group(1).zfill(2)
                    day = date_match.group(2).zfill(2)
                    extracted_date = f"{current_year}-{month}-{day}"

            # 验证提取的日期是否合理
            if extracted_date:
                try:
                    extracted_datetime = datetime.strptime(extracted_date, '%Y-%m-%d')
                    current_datetime = datetime.now()

                    # 计算日期差异
                    date_diff = (extracted_datetime - current_datetime).days

                    # 如果识别的日期大于当前日期3天，则忽略该日期
                    if date_diff > 3:
                        logger.warning(f"识别的日期 {extracted_date} 超出当前日期3天范围，忽略该日期，继续使用上次识别的日期: {self.current_date}")
                        return self.current_date  # 返回上次识别的日期

                    # 更新当前日期
                    self.current_date = extracted_date
                    return extracted_date

                except ValueError as ve:
                    logger.error(f"日期格式验证失败: {ve}, 日期: {extracted_date}")
                    return self.current_date

            return self.current_date  # 如果没有提取到日期，返回上次识别的日期

        except Exception as e:
            logger.error(f"提取日期失败: {e}, 文本: {text}")
            return self.current_date

    def parse_table_live_data(self, td_texts: list, current_date: str = None) -> dict:
        """解析新表格#table_live的比赛数据
        """
        if len(td_texts) < 7:  # 增加列数要求以包含盤字段
            return None

        try:
            # 根据新表格的列位置解析
            league_name = td_texts[1].strip() if len(td_texts) > 1 else ""
            time_str = td_texts[2].strip() if len(td_texts) > 2 else ""
            status = td_texts[3].strip() if len(td_texts) > 3 else ""
            home_team = td_texts[4].strip() if len(td_texts) > 4 else ""
            score = td_texts[5].strip() if len(td_texts) > 5 else ""
            away_team = td_texts[6].strip() if len(td_texts) > 6 else ""
            zs1 = td_texts[9].strip() if len(td_texts) > 9 else ""
            pan_value = td_texts[10].strip() if len(td_texts) > 10 else ""
            zs2 = td_texts[11].strip() if len(td_texts) > 11 else ""

            # 检查盤字段是否为"封"，若是则跳过
            if pan_value == "封":
                logger.debug(f"盤字段为'封'，跳过该行数据: {league_name} - {home_team} vs {away_team}")
                return None

            # 清理队伍名称（去除排名等信息）
            home_team = self.clean_team_name(home_team)
            away_team = self.clean_team_name(away_team)

            # 验证必要字段
            if not league_name or not home_team or not away_team or not time_str:
                logger.debug(f"必要信息不完整，跳过: 联赛={league_name}, 主队={home_team}, 客队={away_team}, 时间={time_str}")
                return None

            # 解析比分
            home_score = None
            away_score = None
            match_status = '未开始'

            if score and score != '-':
                # 解析比分格式 "2 - 1" 或 "0 - 0"
                score_match = re.search(r'(\\d+)\\s*[-–]\\s*(\\d+)', score)
                if score_match:
                    try:
                        home_score = int(score_match.group(1))
                        away_score = int(score_match.group(2))
                    except ValueError:
                        pass

            # 根据状态判断比赛状态
            if status:
                # 检查状态是否为数字（比赛进行时长）
                if status.isdigit():
                    match_status = '进行中'
                elif '下' in status or '上' in status or '中' in status:
                    match_status = '进行中'
                elif '完' in status:
                    match_status = '已结束'
                elif status in ['推迟', '延期']:
                    match_status = '延期'
                elif status in ['取消', '中断', '腰斩']:
                    match_status = '取消'
                else:
                    match_status = '未开始'  # 修正为数据库ENUM中的值

            # 构造完整的比赛时间，确保不为None
            match_datetime = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)  # 默认值

            # 对于已完结的比赛，使用当天日期
            if match_status == '已结束':
                today = datetime.now().strftime('%Y-%m-%d')
                if time_str and re.match(r'\\d{1,2}:\\d{2}', time_str):
                    try:
                        datetime_str = f"{today} {time_str}:00"
                        match_datetime = datetime.strptime(datetime_str, '%Y-%m-%d %H:%M:%S')
                    except Exception as e:
                        logger.error(f"已结束比赛时间解析失败: {e}")
                        # 保持默认值
            else:
                # 对于未开赛或进行中的比赛，使用合并行的日期
                if time_str and current_date:
                    try:
                        if re.match(r'\\d{1,2}:\\d{2}', time_str):
                            # 提取当前比赛的小时
                            time_parts = time_str.split(':')
                            current_hour = int(time_parts[0])
                            
                            # 检测跨天：如果当前时间小于上一场比赛时间，且差值较大（如>12小时），则认为跨天了
                            actual_date = current_date
                            if self.last_match_hour is not None:
                                if current_hour < self.last_match_hour and (self.last_match_hour - current_hour) > 12:
                                    # 跨天了，日期增加一天
                                    from datetime import timedelta
                                    date_obj = datetime.strptime(current_date, '%Y-%m-%d')
                                    date_obj += timedelta(days=1)
                                    actual_date = date_obj.strftime('%Y-%m-%d')
                                    logger.debug(f"检测到跨天：上一场比赛{self.last_match_hour}点，当前比赛{current_hour}点，日期从{current_date}调整为{actual_date}")
                            
                            # 更新上一场比赛的小时
                            self.last_match_hour = current_hour
                            
                            datetime_str = f"{actual_date} {time_str}:00"
                            match_datetime = datetime.strptime(datetime_str, '%Y-%m-%d %H:%M:%S')
                        else:
                            parsed_time = self.parse_match_time(actual_date, time_str)
                            if parsed_time:
                                match_datetime = parsed_time
                    except Exception as e:
                        logger.error(f"时间解析失败: {e}")
                        # 保持默认值
                elif time_str:
                    # 尝试解析时间字符串，使用当前日期
                    try:
                        use_date = current_date if current_date else datetime.now().strftime('%Y-%m-%d')
                        parsed_time = self.parse_match_time(use_date, time_str)
                        if parsed_time:
                            match_datetime = parsed_time
                    except Exception as e:
                        logger.error(f"时间解析失败: {e}")
                        # 保持默认值

            # 确保match_datetime不为None
            if match_datetime is None:
                match_datetime = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)

            return {
                'league_name': league_name,
                'home_team': home_team,
                'away_team': away_team,
                'match_time': match_datetime,
                'home_score': home_score,
                'away_score': away_score,
                'match_status': match_status,
                'pan': pan_value  # 添加盤字段
            }

        except Exception as e:
            logger.error(f"解析table_live数据时出错: {e}, 数据: {td_texts}")
            return None


    def clean_team_name(self, team_name: str) -> str:
        """清理队伍名称，去除排名等信息"""
        if not team_name:
            return ""

        # 去除方括号内的排名信息，如 "[秋1]青年體育會" -> "青年體育會"
        cleaned = re.sub(r'\\[.*?\\]', '', team_name)

        # 去除多余的空格
        cleaned = cleaned.strip()

        return cleaned


    def parse_match_time(self, date_str: str, time_str: str) -> datetime:
        """解析比赛时间
        Args:
            date_str: 日期字符串，格式如 '2024-09-14'
            time_str: 时间字符串，格式如 '18:00' 或其他格式
        Returns:
            datetime: 合并后的日期时间对象，如果time_str不是有效时间格式则返回None
        """
        try:
            time_str = time_str.strip()
            
            # 如果没有提供日期，使用当前日期
            if not date_str:
                date_str = datetime.now().strftime('%Y-%m-%d')

            # 处理简单的时间格式 "18:00"
            if re.match(r'\d{1,2}:\d{2}', time_str):
                try:
                    datetime_str = f"{date_str} {time_str}:00"
                    return datetime.strptime(datetime_str, '%Y-%m-%d %H:%M:%S')
                except ValueError as e:
                    logger.error(f"时间解析失败: {e}, 日期={date_str}, 时间={time_str}")
                    return None

            # 处理 "08月19日 16:00" 格式（忽略日期部分，使用传入的date_str）
            elif re.match(r'\d{1,2}月\d{1,2}日\s+\d{1,2}:\d{2}', time_str):
                time_match = re.search(r'(\d{1,2}):(\d{2})', time_str)
                if time_match:
                    hour = time_match.group(1)
                    minute = time_match.group(2)
                    try:
                        datetime_str = f"{date_str} {hour}:{minute}:00"
                        return datetime.strptime(datetime_str, '%Y-%m-%d %H:%M:%S')
                    except ValueError as e:
                        logger.error(f"时间解析失败: {e}, 日期={date_str}, 时间={hour}:{minute}")
                        return None

            # 处理特殊状态
            elif time_str in ['完', '推迟', '取消', '中断', '腰斩']:
                try:
                    datetime_str = f"{date_str} 00:00:00"
                    return datetime.strptime(datetime_str, '%Y-%m-%d %H:%M:%S')
                except ValueError as e:
                    logger.error(f"特殊状态时间解析失败: {e}, 日期={date_str}")
                    return None

            # 处理完整日期时间格式 "2024-08-19 20:00"（忽略日期部分，使用传入的date_str）
            elif re.match(r'\d{4}-\d{1,2}-\d{1,2}\s+\d{1,2}:\d{2}', time_str):
                time_match = re.search(r'\s+(\d{1,2}:\d{2})', time_str)
                if time_match:
                    time_part = time_match.group(1)
                    try:
                        datetime_str = f"{date_str} {time_part}:00"
                        return datetime.strptime(datetime_str, '%Y-%m-%d %H:%M:%S')
                    except ValueError as e:
                        logger.error(f"完整格式时间解析失败: {e}, 日期={date_str}, 时间={time_part}")
                        return None

            else:
                logger.debug(f"无法识别的时间格式: {time_str}")
                return None

        except Exception as e:
            logger.error(f"解析时间失败: {e}, 日期字符串: {date_str}, 时间字符串: {time_str}")
            return None

    def generate_nano_id(self, length=10):
        # 定义可用字符集
        alphabet = string.ascii_letters + string.digits
        # 生成指定长度的随机字符串
        nano_id = ''.join(secrets.choice(alphabet) for _ in range(length))
        return nano_id