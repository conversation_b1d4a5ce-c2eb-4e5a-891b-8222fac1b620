# Git相关文件
.git
.gitignore
.gitattributes

# Python缓存文件
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# 虚拟环境
venv/
env/
ENV/
env.bak/
venv.bak/

# IDE文件
.vscode/
.idea/
*.swp
*.swo
*~

# 日志文件
logs/
*.log

# 数据文件
data/
*.db
*.sqlite
*.sqlite3

# 临时文件
tmp/
temp/
.tmp/
*.tmp

# 操作系统文件
.DS_Store
Thumbs.db
desktop.ini

# 文档文件
README*.md
docs/
*.md

# 测试文件
tests/
test_*.py
*_test.py

# Jenkins和CI/CD文件
Jenkinsfile*
.jenkins/
.github/

# Docker相关文件
Dockerfile*
docker-compose*.yml
.dockerignore

# 配置文件备份
*.bak
*.backup
*.orig

# 压缩文件
*.zip
*.tar.gz
*.rar

# 媒体文件
*.jpg
*.jpeg
*.png
*.gif
*.mp4
*.avi

# 其他不需要的文件
.coverage
.pytest_cache/
.mypy_cache/
.dmypy.json
dmypy.json