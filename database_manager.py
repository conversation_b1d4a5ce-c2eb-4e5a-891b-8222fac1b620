#!/usr/bin/env python3
"""
数据库管理工具
提供查看、统计和管理比赛数据的核心功能
"""
import asyncio
from datetime import date, timedelta
from mysql_client import MySQLClient
from conf import mysql_setting
from logging_config import setup_module_logging

logger = setup_module_logging(__name__)


class DatabaseManager:
    def __init__(self):
        self.mysql_client = MySQLClient(mysql_setting)

    async def connect(self):
        """连接数据库"""
        await self.mysql_client.connect()

    async def close(self):
        """关闭数据库连接"""
        await self.mysql_client.close()

    async def get_statistics(self):
        """获取数据库统计信息"""
        try:
            async with self.mysql_client.pool.acquire() as conn:
                async with conn.cursor() as cursor:
                    # 总比赛数
                    await cursor.execute("SELECT COUNT(*) FROM race_matches")
                    total_matches = (await cursor.fetchone())[0]
                    
                    # 按状态统计
                    await cursor.execute("""
                        SELECT match_status, COUNT(*) 
                        FROM race_matches 
                        GROUP BY match_status
                    """)
                    status_stats = await cursor.fetchall()
                    
                    # 按联赛统计
                    await cursor.execute("""
                        SELECT league_name, COUNT(*) 
                        FROM race_matches 
                        GROUP BY league_name 
                        ORDER BY COUNT(*) DESC 
                        LIMIT 10
                    """)
                    league_stats = await cursor.fetchall()
                    
                    # 有比分的比赛数
                    await cursor.execute("""
                        SELECT COUNT(*) FROM race_matches 
                        WHERE home_score IS NOT NULL AND away_score IS NOT NULL
                    """)
                    matches_with_scores = (await cursor.fetchone())[0]
                    
                    return {
                        'total_matches': total_matches,
                        'matches_with_scores': matches_with_scores,
                        'matches_without_scores': total_matches - matches_with_scores,
                        'status_stats': status_stats,
                        'league_stats': league_stats
                    }
        except Exception as e:
            logger.error(f"获取统计信息失败: {e}")
            return None

    async def cleanup_old_data(self, days_old: int = 30):
        """清理旧数据"""
        try:
            cutoff_date = date.today() - timedelta(days=days_old)
            
            async with self.mysql_client.pool.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute("""
                        DELETE FROM race_matches 
                        WHERE DATE(match_time) < %s
                    """, (cutoff_date,))
                    
                    deleted_count = cursor.rowcount
                    logger.info(f"清理了 {deleted_count} 条 {days_old} 天前的数据")
                    return deleted_count
        except Exception as e:
            logger.error(f"清理旧数据失败: {e}")
            return 0


async def get_database_stats():
    """获取并返回数据库统计信息"""
    manager = DatabaseManager()
    try:
        await manager.connect()
        stats = await manager.get_statistics()
        return stats
    except Exception as e:
        logger.error(f"获取数据库统计失败: {e}")
        return None
    finally:
        await manager.close()


if __name__ == "__main__":
    # 简单的统计信息输出
    async def main():
        stats = await get_database_stats()
        if stats:
            print(f"总比赛数: {stats['total_matches']}")
            print(f"有比分比赛: {stats['matches_with_scores']}")
            print(f"无比分比赛: {stats['matches_without_scores']}")
            print("\n按状态统计:")
            for status, count in stats['status_stats']:
                print(f"  {status}: {count}")
            print("\n热门联赛:")
            for league, count in stats['league_stats']:
                print(f"  {league}: {count}")
    
    asyncio.run(main())
