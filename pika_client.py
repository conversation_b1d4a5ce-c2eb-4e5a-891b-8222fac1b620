import asyncio
import aio_pika
from aio_pika import Channel, Connection


class PikaClient:
    def __init__(self, cfg):
        self.host = cfg.host
        self.port = cfg.port
        self.user = cfg.user
        self.password = cfg.password
        self.queue = cfg.queue
        self.connection = None
        self.channel = None
        self.url = f"amqp://{self.user}:{self.password}@{self.host}:{self.port}/"

    async def connect(self, loop) -> None:
        try:
            self.connection: Connection = await aio_pika.connect(url=self.url, loop=loop)
            self.channel = await self.connection.channel()
            await self.channel.declare_queue(self.queue, durable=True)
            print('Connected to Rabbit MQ')
        except Exception as e:
            print(f"rabbitmq connect fail {e}")

    async def publish(self, topic, message):
        if self.channel:
            data = aio_pika.Message(body=message.encode())
            await self.channel.default_exchange.publish(data, routing_key=topic)

    async def close(self) -> None:
        if self.channel:
            await self.channel.close()
        if self.connection:
            await self.connection.close()
