import asyncio
from datetime import datetime
from typing import Optional, Dict, Any
import aiomysql
import pymysql
from conf import MySQLConfig
from logging_config import setup_module_logging
from snowflake_id import generate_id

logger = setup_module_logging(__name__)


class MySQLClient:
    def __init__(self, config: MySQLConfig):
        self.config = config
        self.pool = None
        self.connected = False

    async def connect(self) -> None:
        """建立数据库连接池"""
        try:
            self.pool = await aiomysql.create_pool(
                host=self.config.host,
                port=self.config.port,
                user=self.config.user,
                password=self.config.password,
                db=self.config.database,
                charset=self.config.charset,
                autocommit=True,
                minsize=1,
                maxsize=10
            )
            self.connected = True
            logger.info(f"Connected to MySQL database: {self.config.database}")
            
            # 创建表（如果不存在）
            await self.create_tables()
            
        except Exception as e:
            logger.error(f"Failed to connect to MySQL: {e}")
            raise

    async def create_tables(self) -> None:
        """创建数据库表"""
        # 创建比赛数据表
        create_matches_table_sql = """
        CREATE TABLE IF NOT EXISTS race_matches (
            id BIGINT PRIMARY KEY COMMENT '主键ID(雪花算法)',
            league_name VARCHAR(255) NOT NULL COMMENT '联赛名称',
            home_team VARCHAR(255) NOT NULL COMMENT '主队名称',
            away_team VARCHAR(255) NOT NULL COMMENT '客队名称',
            match_time DATETIME NOT NULL COMMENT '比赛时间',
            home_score INT DEFAULT NULL COMMENT '主队得分',
            away_score INT DEFAULT NULL COMMENT '客队得分',
            match_status ENUM('未开始', '进行中', '已结束', '延期', '取消') DEFAULT '未开始' COMMENT '比赛状态',
            analysis_id VARCHAR(20) DEFAULT NULL COMMENT '分析页面ID（去除bh前缀后的值）',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
            INDEX idx_league_name (league_name),
            INDEX idx_match_time (match_time),
            INDEX idx_match_status (match_status),
            INDEX idx_analysis_id (analysis_id),
            UNIQUE KEY unique_match (league_name, home_team, away_team, match_time)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='比赛数据表'
        """
        
        # 创建titan007比赛数据表
        create_titan007_race_table_sql = """
        CREATE TABLE IF NOT EXISTS titan007_race (
            id BIGINT PRIMARY KEY COMMENT '主键ID(雪花算法)',
            race_id VARCHAR(20) NOT NULL COMMENT '比赛ID，来源于analysis链接',
            league_name VARCHAR(255) NOT NULL COMMENT '联赛名称',
            home_team VARCHAR(255) NOT NULL COMMENT '主队名称',
            away_team VARCHAR(255) NOT NULL COMMENT '客队名称',
            match_time DATETIME NOT NULL COMMENT '比赛时间',
            home_score INT(11) DEFAULT NULL COMMENT '主队得分',
            away_score INT(11) DEFAULT NULL COMMENT '客队得分',
            match_status ENUM('未开始','进行中','已结束','延期','取消') NOT NULL DEFAULT '未开始' COMMENT '比赛状态',
            pan VARCHAR(50) DEFAULT NULL COMMENT '盘口信息',
            analysis_id VARCHAR(20) DEFAULT NULL COMMENT '分析ID，来源于tr的id属性',
            created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
            UNIQUE KEY unique_race_id (race_id),
            KEY idx_match_time (match_time),
            KEY idx_league_name (league_name),
            KEY idx_match_status (match_status)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='titan007比赛数据表'
        """
        
        # 创建队伍分析数据表
        create_analysis_table_sql = """
        CREATE TABLE IF NOT EXISTS team_analysis (
            id BIGINT PRIMARY KEY COMMENT '主键ID(雪花算法)',
            team_name VARCHAR(255) NOT NULL COMMENT '队伍名称',
            league_name VARCHAR(255) NOT NULL COMMENT '联赛名称',
            analysis_data JSON COMMENT '队伍分析数据JSON',
            last_analysis_id VARCHAR(20) COMMENT '最后分析的页面ID',
            last_analysis_url VARCHAR(255) COMMENT '最后分析的页面URL',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            UNIQUE KEY unique_team_league (team_name, league_name),
            INDEX idx_team_name (team_name),
            INDEX idx_league_name (league_name),
            INDEX idx_last_analysis_id (last_analysis_id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='队伍分析数据表'
        """
        
        try:
            async with self.pool.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(create_matches_table_sql)
                    await cursor.execute(create_titan007_race_table_sql)
                    await cursor.execute(create_analysis_table_sql)
                    logger.info("Database tables created successfully")
        except Exception as e:
            logger.error(f"Failed to create tables: {e}")
            raise

    async def insert_match(self, league_name: str, home_team: str, away_team: str,
                          match_time: datetime, home_score: int = None,
                          away_score: int = None, match_status: str = '未开始') -> bool:
        """插入比赛数据"""
        if not self.connected or not self.pool:
            await self.connect()

        # 生成雪花ID
        match_id = generate_id()
        
        insert_sql = """
        INSERT IGNORE INTO race_matches
        (id, league_name, home_team, away_team, match_time, home_score, away_score, match_status)
        VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
        """

        try:
            async with self.pool.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(insert_sql, (
                        match_id, league_name, home_team, away_team, match_time,
                        home_score, away_score, match_status
                    ))
                    affected_rows = cursor.rowcount

            if affected_rows > 0:
                score_info = f" ({home_score}-{away_score})" if home_score is not None and away_score is not None else ""
                logger.info(f"✅ 插入比赛: ID={match_id}, {league_name} - {home_team} vs {away_team} at {match_time}{score_info}")
                return True
            else:
                logger.info(f"⚠️ 跳过重复比赛: {league_name} - {home_team} vs {away_team} at {match_time}")
                return False

        except Exception as e:
            logger.error(f"Failed to insert match data: {e}")
            return False

    async def insert_matches_batch(self, matches: list) -> int:
        """批量插入比赛数据"""
        if not self.connected or not self.pool:
            await self.connect()

        if not matches:
            return 0

        insert_sql = """
        INSERT IGNORE INTO race_matches
        (id, league_name, home_team, away_team, match_time, home_score, away_score, match_status)
        VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
        """

        try:
            values = []
            for match in matches:
                match_id = generate_id()  # 为每场比赛生成雪花ID
                values.append((
                    match_id,
                    match['league_name'],
                    match['home_team'],
                    match['away_team'],
                    match['match_time'],
                    match.get('home_score'),
                    match.get('away_score'),
                    match.get('match_status', '未开始')
                ))

            async with self.pool.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.executemany(insert_sql, values)
                    affected_rows = cursor.rowcount

            total_matches = len(matches)
            skipped_matches = total_matches - affected_rows

            if affected_rows > 0:
                logger.info(f"✅ 批量插入成功: 新增 {affected_rows} 场比赛")

            if skipped_matches > 0:
                logger.info(f"⚠️ 跳过重复比赛: {skipped_matches} 场")

            logger.info(f"📊 批量处理完成: 总计 {total_matches} 场，新增 {affected_rows} 场，跳过 {skipped_matches} 场")
            return affected_rows

        except Exception as e:
            logger.error(f"Failed to batch insert matches: {e}")
            return 0

    async def upsert_matches_batch(self, matches: list) -> dict:
        """批量UPSERT比赛数据 - 有则更新，无则插入"""
        if not self.connected or not self.pool:
            await self.connect()

        if not matches:
            return {'inserted': 0, 'updated': 0, 'total': 0}

        # 使用ON DUPLICATE KEY UPDATE语法实现UPSERT
        upsert_sql = """
        INSERT INTO race_matches
        (id, league_name, home_team, away_team, match_time, home_score, away_score, match_status, analysis_id)
        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
        ON DUPLICATE KEY UPDATE
            home_score = CASE 
                WHEN VALUES(home_score) IS NOT NULL THEN VALUES(home_score)
                ELSE home_score
            END,
            away_score = CASE 
                WHEN VALUES(away_score) IS NOT NULL THEN VALUES(away_score)
                ELSE away_score
            END,
            match_status = VALUES(match_status),
            analysis_id = CASE 
                WHEN VALUES(analysis_id) IS NOT NULL THEN VALUES(analysis_id)
                ELSE analysis_id
            END,
            updated_at = CURRENT_TIMESTAMP
        """

        try:
            values = []
            for match in matches:
                match_id = generate_id()  # 为每场比赛生成雪花ID
                values.append((
                    match_id,
                    match['league_name'],
                    match['home_team'],
                    match['away_team'],
                    match['match_time'],
                    match.get('home_score'),
                    match.get('away_score'),
                    match.get('match_status', '未开始'),
                    match.get('analysis_id')
                ))

            async with self.pool.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.executemany(upsert_sql, values)
                    affected_rows = cursor.rowcount

            # MySQL的affected_rows对于ON DUPLICATE KEY UPDATE:
            # 1 = 插入新行, 2 = 更新现有行, 0 = 没有变化
            total_matches = len(matches)
            
            # 估算插入和更新数量（MySQL不提供精确分离统计）
            estimated_inserted = sum(1 for i in range(0, affected_rows, 1) if i % 2 == 0)
            estimated_updated = affected_rows - estimated_inserted

            logger.info(f"📊 批量UPSERT完成: 总计 {total_matches} 场，影响 {affected_rows} 行")
            logger.info(f"✅ 估计新增: {estimated_inserted} 场，更新: {estimated_updated} 场")

            return {
                'total': total_matches,
                'affected': affected_rows,
                'estimated_inserted': estimated_inserted,
                'estimated_updated': estimated_updated
            }

        except Exception as e:
            logger.error(f"Failed to batch upsert matches: {e}")
            return {'total': 0, 'affected': 0, 'estimated_inserted': 0, 'estimated_updated': 0}

    async def upsert_titan007_race_batch(self, matches: list) -> dict:
        """批量UPSERT titan007_race表数据 - 有则更新，无则插入"""
        if not self.connected or not self.pool:
            await self.connect()

        if not matches:
            return {'total': 0, 'affected': 0}
        
        # 过滤掉没有race_id的数据
        valid_matches = [match for match in matches if match.get('race_id')]
        
        if not valid_matches:
            return {'total': 0, 'affected': 0}
        
        # 构建UPSERT SQL语句
        upsert_sql = """
        INSERT INTO titan007_race (
            id, race_id, league_name, home_team, away_team, match_time,
            home_score, away_score, match_status, pan, analysis_id
        ) VALUES (
            %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
        )
        ON DUPLICATE KEY UPDATE
            league_name = VALUES(league_name),
            home_team = VALUES(home_team),
            away_team = VALUES(away_team),
            match_time = VALUES(match_time),
            home_score = VALUES(home_score),
            away_score = VALUES(away_score),
            match_status = VALUES(match_status),
            pan = VALUES(pan),
            analysis_id = VALUES(analysis_id),
            updated_at = CURRENT_TIMESTAMP
        """
        
        try:
            values = []
            for match in valid_matches:
                match_id = generate_id()  # 为每场比赛生成雪花ID
                values.append((
                    match_id,
                    match['race_id'],
                    match['league_name'],
                    match['home_team'],
                    match['away_team'],
                    match['match_time'],
                    match.get('home_score'),
                    match.get('away_score'),
                    match.get('match_status', '未开始'),
                    match.get('pan'),
                    match.get('analysis_id')
                ))

            async with self.pool.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.executemany(upsert_sql, values)
                    affected_rows = cursor.rowcount

            logger.info(f"📊 titan007_race批量UPSERT完成: 总计 {len(valid_matches)} 场，影响 {affected_rows} 行")

            return {
                'total': len(valid_matches),
                'affected': affected_rows
            }
            
        except Exception as e:
            logger.error(f"批量UPSERT titan007_race数据失败: {e}")
            raise e



    async def get_matches_by_date(self, match_date: str) -> list:
        """根据日期查询比赛数据"""
        if not self.connected or not self.pool:
            await self.connect()

        select_sql = """
        SELECT id, league_name, home_team, away_team, match_time,
               home_score, away_score, match_status, created_at
        FROM race_matches
        WHERE DATE(match_time) = %s
        ORDER BY match_time
        """

        try:
            async with self.pool.acquire() as conn:
                async with conn.cursor(aiomysql.DictCursor) as cursor:
                    await cursor.execute(select_sql, (match_date,))
                    results = await cursor.fetchall()
                    return results
        except Exception as e:
            logger.error(f"Failed to query matches: {e}")
            return []

    async def get_matches_by_date_range(self, start_date: str, end_date: str) -> list:
        """根据日期范围查询比赛数据"""
        if not self.connected or not self.pool:
            await self.connect()

        select_sql = """
        SELECT id, league_name, home_team, away_team, match_time,
               home_score, away_score, match_status, created_at
        FROM race_matches
        WHERE DATE(match_time) BETWEEN %s AND %s
        ORDER BY match_time
        """

        try:
            async with self.pool.acquire() as conn:
                async with conn.cursor(aiomysql.DictCursor) as cursor:
                    await cursor.execute(select_sql, (start_date, end_date))
                    results = await cursor.fetchall()
                    return results
        except Exception as e:
            logger.error(f"Failed to query matches by date range: {e}")
            return []

    async def get_matches_without_scores_by_date_range(self, start_date: str, end_date: str) -> list:
        """根据日期范围查询未写入比分的比赛数据"""
        if not self.connected or not self.pool:
            await self.connect()

        select_sql = """
        SELECT id, league_name, home_team, away_team, match_time,
               home_score, away_score, match_status, created_at
        FROM race_matches
        WHERE DATE(match_time) BETWEEN %s AND %s
        AND match_status IN ('进行中','未开始')
        """

        try:
            async with self.pool.acquire() as conn:
                async with conn.cursor(aiomysql.DictCursor) as cursor:
                    await cursor.execute(select_sql, (start_date, end_date))
                    results = await cursor.fetchall()
                    return results
        except Exception as e:
            logger.error(f"Failed to query matches without scores by date range: {e}")
            return []

    async def update_match_score(self, match_id: int, home_score: int, away_score: int, match_status: str = '已结束') -> bool:
        """更新比赛比分"""
        if not self.connected or not self.pool:
            await self.connect()

        update_sql = """
        UPDATE race_matches 
        SET home_score = %s, away_score = %s, match_status = %s, updated_at = CURRENT_TIMESTAMP
        WHERE id = %s
        """

        try:
            async with self.pool.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(update_sql, (home_score, away_score, match_status, match_id))
                    affected_rows = cursor.rowcount

            if affected_rows > 0:
                logger.info(f"✅ 更新比分成功: ID={match_id}, 比分={home_score}-{away_score}")
                return True
            else:
                logger.warning(f"⚠️ 未找到匹配的比赛记录: ID={match_id}")
                return False

        except Exception as e:
            logger.error(f"Failed to update match score: {e}")
            return False

    async def close(self) -> None:
        """关闭数据库连接池"""
        if self.pool:
            self.pool.close()
            await self.pool.wait_closed()
            self.connected = False
            logger.info("MySQL connection pool closed")

    def __del__(self):
        """析构函数"""
        if self.pool and not self.pool._closed:
            try:
                loop = asyncio.get_event_loop()
                if loop.is_running():
                    loop.create_task(self.close())
            except:
                pass
