-- 创建titan007_race数据表
CREATE TABLE IF NOT EXISTS `titan007_race` (
  `race_id` varchar(20) PRIMARY KEY COMMENT '比赛ID，来源于analysis链接，作为主键',
  `league_name` varchar(255) NOT NULL COMMENT '联赛名称',
  `home_team` varchar(255) NOT NULL COMMENT '主队名称',
  `away_team` varchar(255) NOT NULL COMMENT '客队名称',
  `match_time` datetime NOT NULL COMMENT '比赛时间',
  `home_score` int(11) DEFAULT NULL COMMENT '主队得分',
  `away_score` int(11) DEFAULT NULL COMMENT '客队得分',
  `match_status` enum('未开始','进行中','已结束','延期','取消') NOT NULL DEFAULT '未开始' COMMENT '比赛状态',
  `pan` varchar(50) DEFAULT NULL COMMENT '盘口信息',
  `analysis_id` varchar(20) DEFAULT NULL COMMENT '分析ID，来源于tr的id属性',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  KEY `idx_match_time` (`match_time`),
  KEY `idx_league_name` (`league_name`),
  KEY `idx_match_status` (`match_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='titan007比赛数据表';