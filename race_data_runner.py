from pathlib import Path
from datetime import datetime
import re

from browser_runner import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from conf import BASE_DIR, mysql_setting
from mysql_client import MySQLClient

import secrets
import string

from logging_config import setup_module_logging

logger = setup_module_logging(__name__)

class RaceDataRunner:
    def __init__(self):
        pid = self.generate_nano_id()
        file_name = pid + ".json"
        self.base_path = Path(BASE_DIR / "data")
        self.data_file = self.base_path / file_name
        self.mysql_client = MySQLClient(mysql_setting)
        self.current_date = None

    async def start(self):
        # 连接数据库
        await self.mysql_client.connect()
        browser_runner = BrowserRunner(self.process)
        await browser_runner.start()

    async def process(self, page):
        await page.goto("https://bf.7m.com.cn/default_ft.aspx?Classid=&view=all&match=&line=no")
        
        # 等待页面加载
        await page.wait_for_timeout(3000)
        
        table = page.locator("#live_Table")
        trs = table.locator("tr")
        tr_all = await trs.all()

        matches_to_insert = []
        current_date = None
        
        logger.info(f"从#live_Table获取到 {len(tr_all)} 行数据")

        for i, tr in enumerate(tr_all):
            try:
                # 跳过第一行表头
                if i == 0:
                    logger.debug("跳过表头行")
                    continue

                # 获取tr的id属性
                tr_id = await tr.get_attribute("id")
                
                tds = tr.locator("td")
                td_count = await tds.count()
                
                # logger.info(f"📋 处理第{i}行，包含{td_count}个单元格，tr_id={tr_id}")
                
                # 检查是否是合并列的行（可能是日期或提示信息）
                colspan_cell = tr.locator("td[colspan]")
                if await colspan_cell.count() > 0:
                    colspan_text = await colspan_cell.inner_text()
                    colspan_text = colspan_text.strip()
                    # logger.info(f"🔍 第{i}行是合并列: '{colspan_text}'")
                    
                    # 判断是否是日期行（包含年月日信息）
                    if re.match(r'.*\d{4}.*\d{1,2}.*\d{1,2}.*', colspan_text) or re.match(r'.*\d{1,2}月\d{1,2}日.*', colspan_text):
                        current_date = self.extract_date_from_text(colspan_text)
                        # if current_date:
                        #     logger.info(f'✅ 识别到日期: {current_date}')
                        # else:
                        #     logger.warning(f'❌ 日期识别失败: {colspan_text}')
                        continue
                    else:
                        # 其他合并列信息（如比赛提示），跳过
                        # logger.info(f"⏭️ 跳过提示信息: {colspan_text}")
                        continue

                # 解析正常的比赛数据行
                if td_count >= 5:  # live_Table通常有更多列
                    td_texts = []
                    for j in range(td_count):
                        td = tds.nth(j)
                        text = await td.inner_text()
                        td_texts.append(text.strip())

                    # logger.info(f"📊 第{i}行数据: {td_texts}")

                    # 过滤空行
                    if any(text for text in td_texts if text):
                        # 解析比赛数据
                        match_data = self.parse_live_table_data(td_texts, current_date)
                        if match_data:
                            # 提取analysis_id（去除bh前缀）
                            analysis_id = None
                            if tr_id and tr_id.startswith('bh'):
                                analysis_id = tr_id[2:]  # 去除前两个字符"bh"
                            
                            match_data['analysis_id'] = analysis_id
                            matches_to_insert.append(match_data)
                            #logger.info(f"✅ 第{i}行解析成功: {match_data['league_name']} - {match_data['home_team']} vs {match_data['away_team']} ({match_data['match_status']})")

            except Exception as e:
                logger.error(f"❌ 处理第{i}行数据时出错: {e}")
                continue

        # 批量UPSERT数据库（有则更新，无则插入）
        if matches_to_insert:
            result = await self.mysql_client.upsert_matches_batch(matches_to_insert)
            logger.info(f"成功处理 {result['total']} 条比赛数据，影响 {result['affected']} 行")
        else:
            logger.warning("未解析到任何有效的比赛数据")


    def extract_date_from_text(self, text: str) -> str:
        """从文本中提取日期，如果识别的日期大于当前日期3天，则忽略该日期"""
        try:
            extracted_date = None
            
            # 匹配 "2024年8月19日" 格式
            date_match = re.search(r'(\d{4})年(\d{1,2})月(\d{1,2})日', text)
            if date_match:
                year = date_match.group(1)
                month = date_match.group(2).zfill(2)
                day = date_match.group(3).zfill(2)
                extracted_date = f"{year}-{month}-{day}"
            
            # 匹配 "8月19日" 格式
            if not extracted_date:
                date_match = re.search(r'(\d{1,2})月(\d{1,2})日', text)
                if date_match:
                    current_year = datetime.now().year
                    month = date_match.group(1).zfill(2)
                    day = date_match.group(2).zfill(2)
                    extracted_date = f"{current_year}-{month}-{day}"
            
            # 验证提取的日期是否合理
            if extracted_date:
                try:
                    extracted_datetime = datetime.strptime(extracted_date, '%Y-%m-%d')
                    current_datetime = datetime.now()
                    
                    # 计算日期差异
                    date_diff = (extracted_datetime - current_datetime).days
                    
                    # 如果识别的日期大于当前日期3天，则忽略该日期
                    if date_diff > 3:
                        logger.warning(f"识别的日期 {extracted_date} 超出当前日期3天范围，忽略该日期，继续使用上次识别的日期: {self.current_date}")
                        return self.current_date  # 返回上次识别的日期
                    
                    # 更新当前日期
                    self.current_date = extracted_date
                    return extracted_date
                    
                except ValueError as ve:
                    logger.error(f"日期格式验证失败: {ve}, 日期: {extracted_date}")
                    return self.current_date
            
            return self.current_date  # 如果没有提取到日期，返回上次识别的日期
            
        except Exception as e:
            logger.error(f"提取日期失败: {e}, 文本: {text}")
            return self.current_date

    def parse_live_table_data(self, td_texts: list, current_date: str = None) -> dict:
        """解析live_Table的比赛数据
        根据页面分析结果，列的顺序是：
        列1: 空, 列2: 赛事, 列3: 时间, 列4: 状态, 列5: 主队, 列6: 比分, 列7: 客队, 列8: 半场比分
        """
        if len(td_texts) < 7:  # 至少需要7列才能包含基本信息
            return None

        try:
            # 根据页面分析结果的固定列位置解析
            league_name = td_texts[1].strip() if len(td_texts) > 1 else ""  # 赛事
            time_str = td_texts[2].strip() if len(td_texts) > 2 else ""     # 时间
            status = td_texts[3].strip() if len(td_texts) > 3 else ""       # 状态
            home_team = td_texts[4].strip() if len(td_texts) > 4 else ""    # 主队
            score = td_texts[5].strip() if len(td_texts) > 5 else ""        # 比分
            away_team = td_texts[6].strip() if len(td_texts) > 6 else ""    # 客队
            half_score = td_texts[7].strip() if len(td_texts) > 7 else ""   # 半场比分
            
            # 清理队伍名称（去除排名等信息）
            home_team = self.clean_team_name(home_team)
            away_team = self.clean_team_name(away_team)
            
            # 验证必要字段
            if not league_name or not home_team or not away_team or not time_str:
                logger.debug(f"必要信息不完整，跳过: 联赛={league_name}, 主队={home_team}, 客队={away_team}, 时间={time_str}")
                return None
            
            # 解析比分
            home_score = None
            away_score = None
            match_status = '未开始'
            
            if score and score != '-':
                # 解析比分格式 "2 - 1" 或 "0 - 0"
                score_match = re.search(r'(\d+)\s*[-–]\s*(\d+)', score)
                if score_match:
                    try:
                        home_score = int(score_match.group(1))
                        away_score = int(score_match.group(2))
                    except ValueError:
                        pass
            
            # 根据状态判断比赛状态
            if status:
                if '下' in status or '上' in status or '中' in status:
                    match_status = '进行中'
                elif '完' in status:
                    match_status = '已结束'
                elif status in ['推迟', '延期']:
                    match_status = '延期'
                elif status in ['取消', '中断', '腰斩']:
                    match_status = '取消'
                else:
                    match_status = '未开始'  # 修正为数据库ENUM中的值
            # 构造完整的比赛时间
            match_datetime = None
            
            # 对于已完结的比赛，使用当天日期
            if match_status == '已结束':
                today = datetime.now().strftime('%Y-%m-%d')
                if time_str and re.match(r'\d{1,2}:\d{2}', time_str):
                    try:
                        datetime_str = f"{today} {time_str}:00"
                        match_datetime = datetime.strptime(datetime_str, '%Y-%m-%d %H:%M:%S')
                    except Exception as e:
                        logger.error(f"已结束比赛时间解析失败: {e}")
                        match_datetime = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
                else:
                    match_datetime = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
            else:
                # 对于未开赛或进行中的比赛，使用合并行的日期
                if time_str and current_date:
                    try:
                        if re.match(r'\d{1,2}:\d{2}', time_str):
                            datetime_str = f"{current_date} {time_str}:00"
                            match_datetime = datetime.strptime(datetime_str, '%Y-%m-%d %H:%M:%S')
                        else:
                            match_datetime = self.parse_match_time(time_str)
                    except Exception as e:
                        logger.error(f"时间解析失败: {e}")
                        match_datetime = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
                else:
                    # 使用当前日期
                    match_datetime = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)

            return {
                'league_name': league_name,
                'home_team': home_team,
                'away_team': away_team,
                'match_time': match_datetime,
                'home_score': home_score,
                'away_score': away_score,
                'match_status': match_status
            }

        except Exception as e:
            logger.error(f"解析live_table数据时出错: {e}, 数据: {td_texts}")
            return None
    
    def clean_team_name(self, team_name: str) -> str:
        """清理队伍名称，去除排名等信息"""
        if not team_name:
            return ""
        
        # 去除方括号内的排名信息，如 "[秋1]青年體育會" -> "青年體育會"
        cleaned = re.sub(r'\[.*?\]', '', team_name)
        
        # 去除多余的空格
        cleaned = cleaned.strip()
        
        return cleaned


    def parse_match_time(self, time_str: str) -> datetime:
        """解析比赛时间"""
        try:
            time_str = time_str.strip()
            current_year = datetime.now().year

            # 处理 "08月19日 16:00" 格式
            date_time_pattern = r'(\d{1,2})月(\d{1,2})日\s+(\d{1,2}):(\d{2})'
            match = re.match(date_time_pattern, time_str)
            if match:
                month = int(match.group(1))
                day = int(match.group(2))
                hour = int(match.group(3))
                minute = int(match.group(4))

                # 构造完整的日期时间
                try:
                    match_datetime = datetime(current_year, month, day, hour, minute, 0)
                    logger.debug(f"解析时间成功: {time_str} -> {match_datetime}")
                    return match_datetime
                except ValueError as ve:
                    logger.error(f"日期构造失败: {ve}, 年={current_year}, 月={month}, 日={day}, 时={hour}, 分={minute}")
                    return None

            # 处理简单的时间格式 "20:00"
            elif re.match(r'\d{1,2}:\d{2}', time_str):
                if not self.current_date:
                    self.current_date = datetime.now().strftime('%Y-%m-%d')

                time_part = time_str.strip()
                datetime_str = f"{self.current_date} {time_part}:00"
                return datetime.strptime(datetime_str, '%Y-%m-%d %H:%M:%S')

            # 处理特殊状态
            elif time_str in ['完', '推迟', '取消', '中断', '腰斩']:
                if not self.current_date:
                    self.current_date = datetime.now().strftime('%Y-%m-%d')

                datetime_str = f"{self.current_date} 00:00:00"
                return datetime.strptime(datetime_str, '%Y-%m-%d %H:%M:%S')

            # 处理纯日期格式 "08月19日"
            elif re.match(r'(\d{1,2})月(\d{1,2})日', time_str):
                date_pattern = r'(\d{1,2})月(\d{1,2})日'
                match = re.match(date_pattern, time_str)
                if match:
                    month = int(match.group(1))
                    day = int(match.group(2))

                    try:
                        match_datetime = datetime(current_year, month, day, 0, 0, 0)
                        return match_datetime
                    except ValueError as ve:
                        logger.error(f"日期构造失败: {ve}")
                        return None

            # 处理完整日期时间格式 "2024-08-19 20:00"
            elif re.match(r'\d{4}-\d{1,2}-\d{1,2}\s+\d{1,2}:\d{2}', time_str):
                try:
                    return datetime.strptime(time_str, '%Y-%m-%d %H:%M')
                except ValueError:
                    logger.debug(f"日期时间格式解析失败: {time_str}")
                    return None

            else:
                logger.debug(f"无法识别的时间格式: {time_str}")
                return None

        except Exception as e:
            logger.error(f"解析时间失败: {e}, 时间字符串: {time_str}")
            return None

    def generate_nano_id(self, length=10):
        # 定义可用字符集
        alphabet = string.ascii_letters + string.digits
        # 生成指定长度的随机字符串
        nano_id = ''.join(secrets.choice(alphabet) for _ in range(length))
        return nano_id
