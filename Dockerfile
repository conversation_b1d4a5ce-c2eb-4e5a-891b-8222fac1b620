FROM rapidfort/python-chromedriver

WORKDIR /opt/app/

COPY . /opt/app/

#RUN cat > /etc/resolv.conf << 'nameserver *******'

#RUN mv /etc/apt/sources.list /etc/apt/sources.list.bak # 备份原有文件

RUN pip config set global.index-url https://mirrors.aliyun.com/pypi/simple/
#RUN pip config set global.index-url https://pypi.tuna.tsinghua.edu.cn/simple

RUN python.exe -m pip install --upgrade pip

RUN pip install -r requirements.txt

RUN python -m playwright install chromium

EXPOSE 8989

ENTRYPOINT python app.py