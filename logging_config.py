#!/usr/bin/env python3
"""
统一的日志配置模块
解决中文乱码问题
"""
import logging
import logging.handlers
import os
import sys
from pathlib import Path


def setup_logging(name: str = None, log_file: str = None, level: int = logging.INFO):
    """
    设置日志配置，支持中文输出
    
    Args:
        name: logger名称，如果为None则使用root logger
        log_file: 日志文件路径，如果为None则不写入文件
        level: 日志级别
    
    Returns:
        logger对象
    """
    # 获取logger
    logger = logging.getLogger(name)
    
    # 如果已经配置过，直接返回
    if logger.handlers:
        return logger
    
    logger.setLevel(level)
    
    # 创建格式化器
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # 控制台处理器 - 针对IDEA和不同环境的编码问题
    console_handler = None

    # 检测运行环境
    is_idea = 'PYCHARM_HOSTED' in os.environ or 'IDEA_INITIAL_DIRECTORY' in os.environ

    if is_idea:
        # IDEA环境特殊处理
        try:
            # 方法1: 直接使用默认的StreamHandler，但设置编码
            console_handler = logging.StreamHandler()
            console_handler.setFormatter(formatter)
            console_handler.setLevel(level)

            # 强制设置编码
            if hasattr(console_handler.stream, 'reconfigure'):
                console_handler.stream.reconfigure(encoding='utf-8', errors='replace')
        except Exception as e:
            print(f"IDEA环境配置失败: {e}")

    if console_handler is None:
        # 通用环境处理
        try:
            # 尝试重新配置stdout为UTF-8
            if hasattr(sys.stdout, 'reconfigure'):
                sys.stdout.reconfigure(encoding='utf-8', errors='replace')

            console_handler = logging.StreamHandler(sys.stdout)
            console_handler.setFormatter(formatter)
            console_handler.setLevel(level)

        except Exception:
            # 最后的备用方案
            import io
            try:
                # 创建UTF-8编码的文本包装器
                utf8_stdout = io.TextIOWrapper(
                    sys.stdout.buffer,
                    encoding='utf-8',
                    errors='replace',
                    line_buffering=True
                )
                console_handler = logging.StreamHandler(utf8_stdout)
                console_handler.setFormatter(formatter)
                console_handler.setLevel(level)
            except Exception:
                # 如果所有方法都失败，使用默认处理器
                console_handler = logging.StreamHandler()
                console_handler.setFormatter(formatter)
                console_handler.setLevel(level)

    logger.addHandler(console_handler)
    
    # 文件处理器
    if log_file:
        # 确保日志目录存在
        log_path = Path(log_file)
        log_path.parent.mkdir(parents=True, exist_ok=True)
        
        # 使用RotatingFileHandler避免日志文件过大
        file_handler = logging.handlers.RotatingFileHandler(
            log_file,
            maxBytes=10*1024*1024,  # 10MB
            backupCount=5,
            encoding='utf-8'
        )
        file_handler.setFormatter(formatter)
        file_handler.setLevel(level)
        logger.addHandler(file_handler)
    
    return logger


def setup_app_logging():
    """为主应用设置日志"""
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)
    
    return setup_logging(
        name=None,  # root logger
        log_file=log_dir / "app.log",
        level=logging.INFO
    )


def setup_module_logging(module_name: str):
    """为特定模块设置日志"""
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)
    
    return setup_logging(
        name=module_name,
        log_file=log_dir / f"{module_name}.log",
        level=logging.INFO
    )


# 设置环境变量确保Python使用UTF-8
if os.name == 'nt':  # Windows
    os.environ['PYTHONIOENCODING'] = 'utf-8'
    # 设置Windows控制台代码页为UTF-8
    try:
        import subprocess
        subprocess.run(['chcp', '65001'], shell=True, capture_output=True)
    except:
        pass


# 测试函数
def test_logging():
    """测试日志功能"""
    logger = setup_logging("test", "test.log")
    
    logger.info("这是一条中文测试信息")
    logger.warning("这是一条中文警告信息")
    logger.error("这是一条中文错误信息")
    
    # 测试各种中文字符
    logger.info("测试繁体中文：紐卡素奧林匹克女足")
    logger.info("测试日文：サッカー")
    logger.info("测试韩文：축구")
    logger.info("测试特殊符号：⚽🏆🎯")
    
    print("日志测试完成，请检查控制台输出和test.log文件")


if __name__ == "__main__":
    test_logging()
