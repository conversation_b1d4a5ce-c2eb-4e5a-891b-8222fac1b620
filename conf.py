import asyncio
from pathlib import Path
import os
import yaml

from pika_client import PikaC<PERSON>
from yaml_utils import setup_yaml_parser

env_yml = os.environ.get('ENV_NAME', 'dev')
# 打开YAML文件
with open(env_yml+'.yml', 'r', encoding='utf-8') as file:
    setup_yaml_parser()
    # 加载YAML内容
    config_data: dict[str, dict] = yaml.safe_load(file)

BASE_DIR = Path(__file__).parent.resolve()

class RabbitMQConfig:
    host: str
    port: int
    user: str
    password: str
    exchange: str
    queue: str

class MySQLConfig:
    host: str
    port: int
    user: str
    password: str
    database: str
    charset: str

rabbitmq_setting_cfg = config_data.get("rabbit_mq")
rabbitmq_setting = RabbitMQConfig()
rabbitmq_setting.__dict__ = rabbitmq_setting_cfg

mysql_setting_cfg = config_data.get("mysql_db")
mysql_setting = MySQLConfig()
mysql_setting.__dict__ = mysql_setting_cfg

# loop = asyncio.get_event_loop()
# pika_client = PikaClient(rabbitmq_setting)
# asyncio.run(pika_client.connect())
# loop.run_until_complete()